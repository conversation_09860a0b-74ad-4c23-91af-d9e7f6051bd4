<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prevost | DMS (Dealer Management System)</title>

    <!-- Material-UI CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mui/material@5.14.20/umd/material-ui.min.css" rel="stylesheet">

    <!-- Dashboard CSS -->
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <div id="root">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="header-left">
                    <img src="Prevost.png" alt="Prevost Logo" class="header-logo">
                    <div class="header-title">
                        <h1>DMS</h1>
                    </div>
                </div>

                <!-- Header Center - Advanced Search -->
                <div class="header-center">
                    <div class="advanced-search-container">
                        <div class="search-wrapper">
                            <span class="material-icons search-icon">search</span>
                            <input type="text"
                                   id="globalSearch"
                                   class="advanced-search-input"
                                   placeholder="Search anything..."
                                   aria-label="Global search"
                                   autocomplete="off">
                            <div class="search-filters">
                                <button class="filter-btn active" data-filter="all" title="All">
                                    <span class="material-icons">apps</span>
                                </button>
                                <button class="filter-btn" data-filter="vehicles" title="Vehicles">
                                    <span class="material-icons">directions_bus</span>
                                </button>
                                <button class="filter-btn" data-filter="customers" title="Customers">
                                    <span class="material-icons">people</span>
                                </button>
                                <button class="filter-btn" data-filter="reports" title="Reports">
                                    <span class="material-icons">assessment</span>
                                </button>
                            </div>
                            <button class="search-clear" id="searchClear" aria-label="Clear search">
                                <span class="material-icons">close</span>
                            </button>
                        </div>
                        <div class="search-suggestions" id="searchSuggestions">
                            <!-- Dynamic suggestions will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="header-right">
                    <!-- Branch Selection -->
                    <div class="header-dropdown branch-selector" data-tooltip="Switch Branch">
                        <button class="icon-button" id="branchSelector" aria-haspopup="true" aria-expanded="false">
                            <span class="material-icons">business</span>
                        </button>
                        <div class="dropdown-menu" id="branchMenu" role="menu">
                            <div class="dropdown-item active" role="menuitem" data-branch="main">
                                <span class="material-icons">business</span>
                                Main Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="north">
                                <span class="material-icons">business</span>
                                North Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="south">
                                <span class="material-icons">business</span>
                                South Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="east">
                                <span class="material-icons">business</span>
                                East Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="west">
                                <span class="material-icons">business</span>
                                West Branch
                            </div>
                        </div>
                    </div>

                    <!-- Release Notes -->
                    <button class="icon-button" id="releaseNotesButton" data-tooltip="Release Notes">
                        <span class="material-icons">new_releases</span>
                        <span class="notification-badge" id="releaseNotesBadge">2</span>
                    </button>

                    <!-- Dashboard Customization -->
                    <button class="icon-button" id="customizeButton" data-tooltip="Customize Dashboard">
                        <span class="material-icons">tune</span>
                    </button>

                    <!-- Month-end Process -->
                    <button class="icon-button month-end-button" id="monthEndButton" data-tooltip="Month-End Process">
                        <span class="material-icons">event_note</span>
                    </button>

                    <!-- Admin Profile -->
                    <div class="header-dropdown admin-dropdown" data-tooltip="Admin Settings">
                        <button class="admin-profile-button" id="adminProfileButton" aria-haspopup="true" aria-expanded="false">
                            <div class="admin-info">
                                <span class="admin-name" id="userName">Administrator</span>
                                <span class="admin-role" id="userRole">Admin</span>
                            </div>
                            <span class="material-icons dropdown-arrow">expand_more</span>
                        </button>
                        <div class="dropdown-menu admin-menu" id="adminMenu" role="menu">
                            <div class="dropdown-item" role="menuitem" data-action="profile">
                                <span class="material-icons">person</span>
                                Profile Settings
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="preferences">
                                <span class="material-icons">settings</span>
                                Preferences
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="security">
                                <span class="material-icons">security</span>
                                Security Settings
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="notifications">
                                <span class="material-icons">notifications</span>
                                Notifications
                            </div>
                            <div class="dropdown-separator"></div>
                            <div class="dropdown-item" role="menuitem" data-action="help">
                                <span class="material-icons">help</span>
                                Help & Support
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="about">
                                <span class="material-icons">info</span>
                                About DMS
                            </div>
                        </div>
                    </div>

                    <!-- Logout Button -->
                    <button class="icon-button logout-button" id="logoutButton" data-tooltip="Logout">
                        <span class="material-icons">logout</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Horizontal Navigation Menu -->
        <nav class="horizontal-nav">
            <div class="nav-container">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link" data-section="dashboard">
                            <span class="material-icons">home</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="#core" class="nav-link" data-section="core">
                            <span class="material-icons">dashboard</span>
                            <span class="nav-text">Core</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#helpdesk" class="nav-link" data-section="helpdesk">
                            <span class="material-icons">support_agent</span>
                            <span class="nav-text">Helpdesk</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#parts" class="nav-link" data-section="parts">
                            <span class="material-icons">inventory_2</span>
                            <span class="nav-text">Parts</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#service" class="nav-link" data-section="service">
                            <span class="material-icons">build_circle</span>
                            <span class="nav-text">Service</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#tams" class="nav-link" data-section="tams">
                            <span class="material-icons">analytics</span>
                            <span class="nav-text">TAMS</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#scheduler" class="nav-link" data-section="scheduler">
                            <span class="material-icons">schedule</span>
                            <span class="nav-text">Scheduler</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Content Area -->
            <div class="dashboard-content">
                <div class="content-header">
                    <div class="header-left">
                        <h2 class="page-title">Dashboard Overview</h2>
                        <div class="breadcrumb">
                            <span>Home</span>
                            <span class="material-icons">chevron_right</span>
                            <span>Core Dashboard</span>
                        </div>
                    </div>

                    <!-- Dashboard Filter Panel -->
                    <div class="filter-panel">
                        <button class="filter-toggle" id="filterToggle" aria-label="Toggle filters">
                            <span class="material-icons">filter_list</span>
                            <span class="filter-text">Filters</span>
                            <span class="filter-count" id="filterCount">0</span>
                        </button>

                        <div class="filter-dropdown" id="filterDropdown">
                            <div class="filter-header">
                                <h3>Dashboard Filters</h3>
                                <button class="filter-reset" id="filterReset">
                                    <span class="material-icons">refresh</span>
                                    Reset All
                                </button>
                            </div>

                            <div class="filter-section">
                                <label class="filter-label">Region</label>
                                <select class="filter-select" id="regionFilter">
                                    <option value="all">All Regions</option>
                                    <option value="north-america">North America</option>
                                    <option value="europe">Europe</option>
                                    <option value="asia-pacific">Asia Pacific</option>
                                    <option value="latin-america">Latin America</option>
                                </select>
                            </div>

                            <div class="filter-section">
                                <label class="filter-label">Branch</label>
                                <select class="filter-select" id="branchFilter">
                                    <option value="all">All Branches</option>
                                    <option value="main">Main Branch</option>
                                    <option value="north">North Branch</option>
                                    <option value="south">South Branch</option>
                                    <option value="east">East Branch</option>
                                    <option value="west">West Branch</option>
                                </select>
                            </div>

                            <div class="filter-section">
                                <label class="filter-label">Information Type</label>
                                <div class="filter-checkboxes">
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="primaryMetrics" checked>
                                        <span class="checkbox-custom"></span>
                                        <span class="checkbox-label">Primary Metrics</span>
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="tamsData" checked>
                                        <span class="checkbox-custom"></span>
                                        <span class="checkbox-label">TAMS Data</span>
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="sapInterface" checked>
                                        <span class="checkbox-custom"></span>
                                        <span class="checkbox-label">SAP Interface Status</span>
                                    </label>
                                </div>
                            </div>

                            <div class="filter-section">
                                <label class="filter-label">Date Range</label>
                                <div class="date-range-inputs">
                                    <input type="date" id="startDate" class="filter-date" value="">
                                    <span class="date-separator">to</span>
                                    <input type="date" id="endDate" class="filter-date" value="">
                                </div>
                            </div>

                            <div class="filter-section">
                                <label class="filter-label">Status Filter</label>
                                <select class="filter-select" id="statusFilter">
                                    <option value="all">All Statuses</option>
                                    <option value="active">Active Only</option>
                                    <option value="pending">Pending Only</option>
                                    <option value="completed">Completed Only</option>
                                    <option value="error">Error Status</option>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button class="btn-apply" id="applyFilters">Apply Filters</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SVG Gradient Definitions -->
                <svg width="0" height="0" style="position: absolute;">
                    <defs>
                        <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#0284c7;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="successGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                </svg>

                <!-- Dashboard Cards -->
                <div class="dashboard-cards">
                    <!-- Primary Business Metrics -->
                    <div class="dashboard-card primary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">request_quote</span>
                            </div>
                            <div class="trend-indicator trend-up">
                                <span class="material-icons">trending_up</span>
                                +12.5%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Quotation Summary</h3>
                            <div class="card-main-content">
                                <p class="card-value">1,247</p>
                                <div class="progress-circle">
                                    <svg viewBox="0 0 36 36">
                                        <circle class="progress-bg" cx="18" cy="18" r="16"></circle>
                                        <circle class="progress-bar" cx="18" cy="18" r="16"
                                                stroke-dasharray="68.5, 100" stroke-dashoffset="0"></circle>
                                    </svg>
                                    <div class="progress-text">68.5%</div>
                                </div>
                            </div>
                            <p class="card-metric">Conversion Ratio <span class="metric-value">68.5%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">build</span>
                            </div>
                            <div class="trend-indicator trend-up">
                                <span class="material-icons">trending_up</span>
                                +8.2%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Work Order Summary</h3>
                            <div class="card-main-content">
                                <p class="card-value">856</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 60%"></div>
                                    <div class="chart-bar" style="height: 80%"></div>
                                    <div class="chart-bar" style="height: 45%"></div>
                                    <div class="chart-bar" style="height: 90%"></div>
                                    <div class="chart-bar" style="height: 75%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 85%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Active Orders <span class="metric-value">142</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">verified_user</span>
                            </div>
                            <div class="trend-indicator trend-down">
                                <span class="material-icons">trending_down</span>
                                -2.1%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Warranty Summary</h3>
                            <div class="card-main-content">
                                <p class="card-value">324</p>
                                <div class="progress-circle">
                                    <svg viewBox="0 0 36 36">
                                        <circle class="progress-bg" cx="18" cy="18" r="16"></circle>
                                        <circle class="progress-bar" cx="18" cy="18" r="16"
                                                stroke-dasharray="15.2, 100" stroke-dashoffset="0"></circle>
                                    </svg>
                                    <div class="progress-text">15.2%</div>
                                </div>
                            </div>
                            <p class="card-metric">Claimed Workorders <span class="metric-value">15.2%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">receipt</span>
                            </div>
                            <div class="trend-indicator trend-up">
                                <span class="material-icons">trending_up</span>
                                +15.7%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Invoice Summary</h3>
                            <div class="card-main-content">
                                <p class="card-value">$2.8M</p>
                                <div class="progress-circle">
                                    <svg viewBox="0 0 36 36">
                                        <circle class="progress-bg" cx="18" cy="18" r="16"></circle>
                                        <circle class="progress-bar" cx="18" cy="18" r="16"
                                                stroke-dasharray="92.3, 100" stroke-dashoffset="0"></circle>
                                    </svg>
                                    <div class="progress-text">92.3%</div>
                                </div>
                            </div>
                            <p class="card-metric">Invoiced Work Orders <span class="metric-value">92.3%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">receipt_long</span>
                            </div>
                            <div class="trend-indicator trend-up">
                                <span class="material-icons">trending_up</span>
                                +6.4%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Internal Invoice Summary</h3>
                            <div class="card-main-content">
                                <p class="card-value">$485K</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 70%"></div>
                                    <div class="chart-bar" style="height: 85%"></div>
                                    <div class="chart-bar" style="height: 60%"></div>
                                    <div class="chart-bar" style="height: 95%"></div>
                                    <div class="chart-bar" style="height: 80%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 90%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Internal Invoiced <span class="metric-value">78.9%</span></p>
                        </div>
                    </div>

                    <!-- Secondary Metrics -->
                    <div class="dashboard-card secondary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">analytics</span>
                            </div>
                            <div class="trend-indicator trend-up">
                                <span class="material-icons">trending_up</span>
                                +1.2%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">TAMS Performance</h3>
                            <div class="card-main-content">
                                <p class="card-value">94.7%</p>
                                <div class="progress-circle">
                                    <svg viewBox="0 0 36 36">
                                        <circle class="progress-bg" cx="18" cy="18" r="16"></circle>
                                        <circle class="progress-bar" cx="18" cy="18" r="16"
                                                stroke-dasharray="94.7, 100" stroke-dashoffset="0"></circle>
                                    </svg>
                                    <div class="progress-text">94.7%</div>
                                </div>
                            </div>
                            <p class="card-metric">Target: 95% <span class="metric-value">-0.3%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card secondary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">trending_down</span>
                            </div>
                            <div class="trend-indicator trend-down">
                                <span class="material-icons">trending_down</span>
                                -2.3%
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">TAMS Deviation</h3>
                            <div class="card-main-content">
                                <p class="card-value">-2.3%</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 95%"></div>
                                    <div class="chart-bar" style="height: 88%"></div>
                                    <div class="chart-bar" style="height: 82%"></div>
                                    <div class="chart-bar" style="height: 75%"></div>
                                    <div class="chart-bar" style="height: 70%"></div>
                                    <div class="chart-bar" style="height: 68%"></div>
                                </div>
                            </div>
                            <p class="card-metric">From Target <span class="metric-value">-2.3%</span></p>
                        </div>
                    </div>

                    <!-- SAP Interface Summary Cards -->
                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">receipt</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Online</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Invoice</p>
                            <div class="card-main-content">
                                <p class="card-value">Synchronized</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Last Sync <span class="metric-value">2 min ago</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">inventory</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Online</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Parts Issues</p>
                            <div class="card-main-content">
                                <p class="card-value">Synchronized</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Last Sync <span class="metric-value">5 min ago</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">keyboard_return</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator warning"></span>
                                <span class="status-text">Pending</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Parts Returns</p>
                            <div class="card-main-content">
                                <p class="card-value">Pending</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 80%"></div>
                                    <div class="chart-bar" style="height: 60%"></div>
                                    <div class="chart-bar" style="height: 40%"></div>
                                    <div class="chart-bar" style="height: 20%"></div>
                                    <div class="chart-bar" style="height: 10%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Queue <span class="metric-value">12 items</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">category</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Online</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Parts Master</p>
                            <div class="card-main-content">
                                <p class="card-value">Synchronized</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Last Sync <span class="metric-value">1 min ago</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">directions_bus</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Online</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Vehicle Master</p>
                            <div class="card-main-content">
                                <p class="card-value">Synchronized</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Last Sync <span class="metric-value">3 min ago</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">people</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator error"></span>
                                <span class="status-text">Error</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Customer Master</p>
                            <div class="card-main-content">
                                <p class="card-value">Error</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 80%"></div>
                                    <div class="chart-bar" style="height: 60%"></div>
                                    <div class="chart-bar" style="height: 40%"></div>
                                    <div class="chart-bar" style="height: 20%"></div>
                                    <div class="chart-bar" style="height: 10%"></div>
                                    <div class="chart-bar" style="height: 5%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Failed <span class="metric-value">15 min ago</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <span class="material-icons">security</span>
                            </div>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Online</span>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface</h3>
                            <p class="card-subtitle">Warranty</p>
                            <div class="card-main-content">
                                <p class="card-value">Synchronized</p>
                                <div class="mini-chart">
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                    <div class="chart-bar" style="height: 100%"></div>
                                </div>
                            </div>
                            <p class="card-metric">Last Sync <span class="metric-value">4 min ago</span></p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-section">
                    <h3 class="section-title">Recent Activity</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">add_circle</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New vehicle added to inventory: Prevost H3-45</p>
                                <p class="activity-time">2 hours ago</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">person_add</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New customer registered: Transit Solutions Inc.</p>
                                <p class="activity-time">4 hours ago</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">sell</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">Sale completed: Prevost X3-45 to Metro Transit</p>
                                <p class="activity-time">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="dashboard-footer">
            <div class="footer-content">
                <p>&copy; 2025 Prevost. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#" class="footer-link">Privacy Policy</a>
                    <span class="footer-separator">|</span>
                    <a href="#" class="footer-link">Terms of Service</a>
                    <span class="footer-separator">|</span>
                    <a href="#" class="footer-link">Support</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Month-End Process Modal -->
    <div class="modal-overlay" id="monthEndModal" role="dialog" aria-labelledby="monthEndTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="monthEndTitle">Month-End Process</h2>
                <button class="modal-close" id="closeMonthEnd" aria-label="Close month-end process">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="month-end-section">
                    <h3>Month-End Actions</h3>
                    <p class="section-description">Execute month-end processes to finalize the current period.</p>

                    <div class="action-grid">
                        <button class="action-btn primary" data-action="generate-reports">
                            <span class="material-icons">assessment</span>
                            <span class="action-text">Generate Reports</span>
                            <span class="action-description">Create month-end financial and operational reports</span>
                        </button>

                        <button class="action-btn secondary" data-action="reconcile-inventory">
                            <span class="material-icons">inventory</span>
                            <span class="action-text">Reconcile Inventory</span>
                            <span class="action-description">Perform inventory reconciliation and adjustments</span>
                        </button>

                        <button class="action-btn warning" data-action="close-period">
                            <span class="material-icons">lock</span>
                            <span class="action-text">Close Period</span>
                            <span class="action-description">Finalize and lock the current accounting period</span>
                        </button>

                        <button class="action-btn info" data-action="export-data">
                            <span class="material-icons">download</span>
                            <span class="action-text">Export Data</span>
                            <span class="action-description">Export month-end data for external systems</span>
                        </button>
                    </div>

                    <div class="month-end-status">
                        <h4>Process Status</h4>
                        <div class="status-list">
                            <div class="status-item completed">
                                <span class="material-icons">check_circle</span>
                                <span>Daily reconciliation completed</span>
                            </div>
                            <div class="status-item completed">
                                <span class="material-icons">check_circle</span>
                                <span>Inventory counts verified</span>
                            </div>
                            <div class="status-item pending">
                                <span class="material-icons">schedule</span>
                                <span>Final reports pending</span>
                            </div>
                            <div class="status-item pending">
                                <span class="material-icons">schedule</span>
                                <span>Period closure pending</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Release Notes Modal -->
    <div class="modal-overlay" id="releaseNotesModal" role="dialog" aria-labelledby="releaseNotesTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="releaseNotesTitle">Release Notes</h2>
                <button class="modal-close" id="closeReleaseNotes" aria-label="Close release notes">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="release-notes-tabs">
                    <button class="tab-button active" data-tab="current">Current Release</button>
                    <button class="tab-button" data-tab="previous">Previous Releases</button>
                </div>
                <div class="tab-content active" id="currentTab">
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.1.0 - January 2025</h3>
                            <span class="release-date">Released: January 15, 2025</span>
                        </div>
                        <div class="release-body">
                            <h4>New Features</h4>
                            <ul>
                                <li>Enhanced dashboard customization options</li>
                                <li>Improved search functionality across all modules</li>
                                <li>New month-end processing workflow</li>
                                <li>Advanced reporting capabilities</li>
                            </ul>
                            <h4>Improvements</h4>
                            <ul>
                                <li>Faster loading times for inventory management</li>
                                <li>Better mobile responsiveness</li>
                                <li>Enhanced security features</li>
                            </ul>
                            <h4>Bug Fixes</h4>
                            <ul>
                                <li>Fixed issue with customer data export</li>
                                <li>Resolved branch switching delays</li>
                                <li>Corrected sales report calculations</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="tab-content" id="previousTab">
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.0.5 - December 2024</h3>
                            <span class="release-date">Released: December 20, 2024</span>
                        </div>
                        <div class="release-body">
                            <h4>Bug Fixes</h4>
                            <ul>
                                <li>Fixed critical security vulnerability</li>
                                <li>Resolved database connection issues</li>
                                <li>Improved error handling</li>
                            </ul>
                        </div>
                    </div>
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.0.0 - November 2024</h3>
                            <span class="release-date">Released: November 15, 2024</span>
                        </div>
                        <div class="release-body">
                            <h4>Major Release</h4>
                            <ul>
                                <li>Complete UI redesign</li>
                                <li>New dashboard architecture</li>
                                <li>Enhanced user management</li>
                                <li>Improved performance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Customization Modal -->
    <div class="modal-overlay" id="customizeModal" role="dialog" aria-labelledby="customizeTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="customizeTitle">Customize Dashboard</h2>
                <button class="modal-close" id="closeCustomize" aria-label="Close customization">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="customize-section">
                    <h3>Layout Options</h3>
                    <div class="layout-options">
                        <div class="layout-option active" data-layout="default">
                            <div class="layout-preview">
                                <div class="preview-grid">
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                </div>
                            </div>
                            <span>Default Grid</span>
                        </div>
                        <div class="layout-option" data-layout="compact">
                            <div class="layout-preview">
                                <div class="preview-grid compact">
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                </div>
                            </div>
                            <span>Compact View</span>
                        </div>
                        <div class="layout-option" data-layout="wide">
                            <div class="layout-preview">
                                <div class="preview-grid wide">
                                    <div class="preview-card wide"></div>
                                    <div class="preview-card wide"></div>
                                </div>
                            </div>
                            <span>Wide Cards</span>
                        </div>
                    </div>
                </div>
                <div class="customize-section">
                    <h3>Widget Visibility</h3>
                    <div class="widget-toggles">
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="vehicles">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Total Vehicles</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="sales">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Monthly Sales</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="customers">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Active Customers</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="branches">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Active Branches</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="activity">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Recent Activity</span>
                        </label>
                    </div>
                </div>
                <div class="customize-section">
                    <h3>Theme Options</h3>
                    <div class="theme-options">
                        <div class="theme-option active" data-theme="default">
                            <div class="theme-preview default"></div>
                            <span>Default</span>
                        </div>
                        <div class="theme-option" data-theme="dark">
                            <div class="theme-preview dark"></div>
                            <span>Dark Mode</span>
                        </div>
                        <div class="theme-option" data-theme="blue">
                            <div class="theme-preview blue"></div>
                            <span>Blue Theme</span>
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" id="resetCustomization">Reset to Default</button>
                    <button class="btn-primary" id="saveCustomization">Save Changes</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Accessibility Live Region -->
    <div id="live-region" class="sr-only" aria-live="polite" aria-atomic="true"></div>

    <!-- Dashboard JavaScript -->
    <script src="dashboard.js"></script>
</body>
</html>
