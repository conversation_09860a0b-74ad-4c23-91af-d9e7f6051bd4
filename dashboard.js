// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// Initialize dashboard functionality
function initializeDashboard() {
    // Load user information from URL parameters or localStorage
    loadUserInfo();

    // Initialize navigation
    initializeNavigation();

    // Initialize header functionality
    initializeHeaderComponents();

    // Initialize logout functionality
    initializeLogout();

    // Initialize dashboard interactions
    initializeDashboardInteractions();

    // Load dashboard data
    loadDashboardData();
}

// Initialize navigation functionality
function initializeNavigation() {
    // Initialize horizontal navigation
    initializeHorizontalNavigation();

    // Initialize breadcrumb navigation
    initializeBreadcrumbNavigation();

    // Set initial navigation state
    setInitialNavigationState();
}

// Initialize breadcrumb navigation
function initializeBreadcrumbNavigation() {
    // Add click handlers to breadcrumb items for navigation
    const breadcrumbItems = document.querySelectorAll('.breadcrumb span:not(.material-icons)');
    breadcrumbItems.forEach((item, index) => {
        if (index === 0) { // Home breadcrumb
            item.style.cursor = 'pointer';
            item.addEventListener('click', function() {
                navigateToSection('dashboard');
            });
        }
    });
}

// Set initial navigation state
function setInitialNavigationState() {
    // Check URL hash or default to dashboard
    const hash = window.location.hash.substring(1);
    const section = hash || 'dashboard';

    // Update navigation to reflect current section
    updateNavigationState(section);
    updateDashboardSection(section);
}

// Update navigation state
function updateNavigationState(section) {
    const navItems = document.querySelectorAll('.horizontal-nav .nav-item');
    navItems.forEach(item => {
        const link = item.querySelector('.nav-link');
        if (link && link.dataset.section === section) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

// Navigate to a specific section
function navigateToSection(section) {
    // Update URL hash
    window.location.hash = section;

    // Update navigation state
    updateNavigationState(section);

    // Update dashboard content
    updateDashboardSection(section);
}

// Load user information
function loadUserInfo() {
    // Get user info from URL parameters or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const userName = urlParams.get('user') || localStorage.getItem('dashboard_user_name') || 'Administrator';
    const userRole = urlParams.get('role') || localStorage.getItem('dashboard_user_role') || 'Admin';

    // Update UI
    document.getElementById('userName').textContent = userName;
    document.getElementById('userRole').textContent = userRole;

    // Store in localStorage for future use
    localStorage.setItem('dashboard_user_name', userName);
    localStorage.setItem('dashboard_user_role', userRole);
}

// Initialize header components
function initializeHeaderComponents() {
    // Remove old search initialization - using enhanced search instead
    initializeBranchSelector();
    initializeReleaseNotes();
    initializeCustomization();
    initializeMonthEnd();
    initializeAdminProfile();
    initializeLogoutButton();
    initializeHorizontalNavigation();
    initializeFilterPanel();
}

// Old search function removed - using enhanced search component instead

// Initialize branch selector
function initializeBranchSelector() {
    const branchSelector = document.getElementById('branchSelector');
    const branchMenu = document.getElementById('branchMenu');

    branchSelector.addEventListener('click', function() {
        toggleDropdown(this, branchMenu);
    });

    // Handle branch selection
    branchMenu.addEventListener('click', function(e) {
        const branchItem = e.target.closest('.dropdown-item');
        if (branchItem) {
            // Remove active class from all items
            this.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to selected item
            branchItem.classList.add('active');

            // Update tooltip
            const branchText = branchItem.textContent.trim();
            branchSelector.setAttribute('data-tooltip', branchText);

            // Close dropdown
            closeDropdown(branchSelector, branchMenu);

            // Switch branch (placeholder functionality)
            switchBranch(branchItem.dataset.branch);
        }
    });
}

// Initialize release notes
function initializeReleaseNotes() {
    const releaseNotesButton = document.getElementById('releaseNotesButton');
    const releaseNotesModal = document.getElementById('releaseNotesModal');
    const closeReleaseNotes = document.getElementById('closeReleaseNotes');

    releaseNotesButton.addEventListener('click', function() {
        showModal(releaseNotesModal);
        // Mark release notes as read
        const badge = document.getElementById('releaseNotesBadge');
        if (badge) {
            badge.style.display = 'none';
        }
    });

    closeReleaseNotes.addEventListener('click', function() {
        hideModal(releaseNotesModal);
    });

    // Initialize tabs
    initializeReleaseTabs();
}

// Initialize customization
function initializeCustomization() {
    const customizeButton = document.getElementById('customizeButton');
    const customizeModal = document.getElementById('customizeModal');
    const closeCustomize = document.getElementById('closeCustomize');
    const saveCustomization = document.getElementById('saveCustomization');
    const resetCustomization = document.getElementById('resetCustomization');

    customizeButton.addEventListener('click', function() {
        showModal(customizeModal);
    });

    closeCustomize.addEventListener('click', function() {
        hideModal(customizeModal);
    });

    saveCustomization.addEventListener('click', function() {
        saveCustomizationSettings();
        hideModal(customizeModal);
    });

    resetCustomization.addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all customizations to default?')) {
            resetCustomizationSettings();
        }
    });

    // Initialize customization options
    initializeCustomizationOptions();
}

// Initialize month-end process
function initializeMonthEnd() {
    const monthEndButton = document.getElementById('monthEndButton');
    const monthEndModal = document.getElementById('monthEndModal');
    const closeMonthEnd = document.getElementById('closeMonthEnd');

    monthEndButton.addEventListener('click', function() {
        showModal(monthEndModal);
    });

    closeMonthEnd.addEventListener('click', function() {
        hideModal(monthEndModal);
    });

    // Initialize month-end actions
    initializeMonthEndActions();
}

// Initialize admin profile
function initializeAdminProfile() {
    const adminProfileButton = document.getElementById('adminProfileButton');
    const adminMenu = document.getElementById('adminMenu');

    adminProfileButton.addEventListener('click', function() {
        toggleDropdown(this, adminMenu);
    });

    // Handle admin menu actions
    adminMenu.addEventListener('click', function(e) {
        const menuItem = e.target.closest('.dropdown-item');
        if (menuItem) {
            const action = menuItem.dataset.action;
            closeDropdown(adminProfileButton, adminMenu);
            handleAdminAction(action);
        }
    });
}

// Initialize logout button
function initializeLogoutButton() {
    const logoutButton = document.getElementById('logoutButton');

    logoutButton.addEventListener('click', function() {
        handleLogout();
    });
}

// Initialize horizontal navigation
function initializeHorizontalNavigation() {
    const navLinks = document.querySelectorAll('.horizontal-nav .nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the target section
            const section = this.dataset.section;

            // Navigate to the section
            navigateToSection(section);
        });
    });

    // Handle browser back/forward navigation
    window.addEventListener('hashchange', function() {
        const hash = window.location.hash.substring(1);
        const section = hash || 'dashboard';
        updateNavigationState(section);
        updateDashboardSection(section);
    });
}

// Initialize filter panel
function initializeFilterPanel() {
    const filterToggle = document.getElementById('filterToggle');
    const filterDropdown = document.getElementById('filterDropdown');
    const filterReset = document.getElementById('filterReset');
    const applyFilters = document.getElementById('applyFilters');
    const regionFilter = document.getElementById('regionFilter');
    const branchFilter = document.getElementById('branchFilter');

    // Enhanced filter toggle with keyboard support
    filterToggle.addEventListener('click', function() {
        const isOpen = filterDropdown.classList.contains('show');

        if (isOpen) {
            closeFilterPanel();
        } else {
            openFilterPanel();
        }
    });

    // Keyboard support for filter toggle
    filterToggle.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
        } else if (e.key === 'Escape') {
            closeFilterPanel();
        }
    });

    // Enhanced keyboard navigation within filter panel
    filterDropdown.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeFilterPanel();
            filterToggle.focus();
        }
    });

    // Close filter panel when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.filter-panel')) {
            closeFilterPanel();
        }
    });

    // Handle window resize to reposition filter dropdown if open
    window.addEventListener('resize', function() {
        const filterDropdown = document.getElementById('filterDropdown');
        if (filterDropdown && filterDropdown.classList.contains('show')) {
            adjustFilterDropdownPosition(filterDropdown);
        }
    });

    // Region filter change updates branch options
    regionFilter.addEventListener('change', function() {
        updateBranchOptions(this.value);
        updateFilterCount();
    });

    // Branch filter change
    branchFilter.addEventListener('change', function() {
        updateFilterCount();
    });

    // Information type checkboxes
    const checkboxes = document.querySelectorAll('.filter-checkboxes input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateFilterCount();
        });
    });

    // Date range inputs
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const statusFilter = document.getElementById('statusFilter');

    if (startDate) {
        startDate.addEventListener('change', function() {
            updateFilterCount();
            validateDateRange();
        });
    }

    if (endDate) {
        endDate.addEventListener('change', function() {
            updateFilterCount();
            validateDateRange();
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            updateFilterCount();
        });
    }

    // Reset filters
    filterReset.addEventListener('click', function() {
        resetAllFilters();
    });

    // Apply filters
    applyFilters.addEventListener('click', function() {
        applyDashboardFilters();
        closeFilterPanel();
    });

    // Load saved filters
    loadSavedFilters();

    // Set default date range (last 30 days)
    setDefaultDateRange();
}

// Enhanced filter panel utility functions
function openFilterPanel() {
    const filterToggle = document.getElementById('filterToggle');
    const filterDropdown = document.getElementById('filterDropdown');

    // Check if dropdown would overflow viewport and adjust positioning
    adjustFilterDropdownPosition(filterDropdown);

    filterToggle.classList.add('active');
    filterDropdown.classList.add('show');

    // Add subtle animation delay for form elements
    const filterSections = filterDropdown.querySelectorAll('.filter-section');
    filterSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(10px)';
        setTimeout(() => {
            section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 50);
    });

    // Announce to screen readers
    announceToScreenReader('Filter panel opened');
}

// Function to adjust filter dropdown position to prevent viewport overflow
function adjustFilterDropdownPosition(filterDropdown) {
    const filterPanel = filterDropdown.closest('.filter-panel');
    if (!filterPanel) return;

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const panelRect = filterPanel.getBoundingClientRect();
    const dropdownWidth = 420; // Max width from CSS

    // Calculate if dropdown would overflow left edge
    const wouldOverflowLeft = panelRect.right - dropdownWidth < 0;

    // Calculate if dropdown would overflow right edge
    const wouldOverflowRight = panelRect.right > viewportWidth;

    // Remove existing position classes
    filterDropdown.classList.remove('position-left');

    // Apply appropriate positioning
    if (wouldOverflowLeft || (panelRect.left + dropdownWidth > viewportWidth)) {
        // Position dropdown to the left of the toggle button
        filterDropdown.classList.add('position-left');
    }

    // For very small screens, ensure dropdown doesn't exceed viewport
    if (viewportWidth < 480) {
        filterDropdown.style.left = '1rem';
        filterDropdown.style.right = '1rem';
        filterDropdown.style.maxWidth = 'calc(100vw - 2rem)';
        filterDropdown.style.minWidth = 'calc(100vw - 2rem)';
    } else {
        // Reset inline styles for larger screens
        filterDropdown.style.left = '';
        filterDropdown.style.right = '';
        filterDropdown.style.maxWidth = '';
        filterDropdown.style.minWidth = '';
    }
}

function closeFilterPanel() {
    const filterToggle = document.getElementById('filterToggle');
    const filterDropdown = document.getElementById('filterDropdown');

    filterToggle.classList.remove('active');
    filterDropdown.classList.remove('show');

    // Reset section animations
    const filterSections = filterDropdown.querySelectorAll('.filter-section');
    filterSections.forEach(section => {
        section.style.opacity = '';
        section.style.transform = '';
        section.style.transition = '';
    });

    // Announce to screen readers
    announceToScreenReader('Filter panel closed');
}

// Accessibility helper function
function announceToScreenReader(message) {
    const liveRegion = document.getElementById('live-region');
    if (liveRegion) {
        liveRegion.textContent = message;
        setTimeout(() => {
            liveRegion.textContent = '';
        }, 1000);
    }
}

function updateBranchOptions(region) {
    const branchFilter = document.getElementById('branchFilter');
    const branchOptions = {
        'all': [
            { value: 'all', text: 'All Branches' },
            { value: 'main', text: 'Main Branch' },
            { value: 'north', text: 'North Branch' },
            { value: 'south', text: 'South Branch' },
            { value: 'east', text: 'East Branch' },
            { value: 'west', text: 'West Branch' }
        ],
        'north-america': [
            { value: 'all', text: 'All Branches' },
            { value: 'main', text: 'Main Branch' },
            { value: 'north', text: 'North Branch' },
            { value: 'east', text: 'East Branch' }
        ],
        'europe': [
            { value: 'all', text: 'All Branches' },
            { value: 'west', text: 'West Branch' }
        ],
        'asia-pacific': [
            { value: 'all', text: 'All Branches' },
            { value: 'south', text: 'South Branch' }
        ],
        'latin-america': [
            { value: 'all', text: 'All Branches' },
            { value: 'south', text: 'South Branch' }
        ]
    };

    const options = branchOptions[region] || branchOptions['all'];

    // Clear existing options
    branchFilter.innerHTML = '';

    // Add new options
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        branchFilter.appendChild(optionElement);
    });
}

function updateFilterCount() {
    const regionFilter = document.getElementById('regionFilter');
    const branchFilter = document.getElementById('branchFilter');
    const checkboxes = document.querySelectorAll('.filter-checkboxes input[type="checkbox"]');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const statusFilter = document.getElementById('statusFilter');
    const filterCount = document.getElementById('filterCount');

    let count = 0;

    // Count active filters
    if (regionFilter && regionFilter.value !== 'all') count++;
    if (branchFilter && branchFilter.value !== 'all') count++;
    if (startDate && startDate.value) count++;
    if (endDate && endDate.value) count++;
    if (statusFilter && statusFilter.value !== 'all') count++;

    // Count unchecked information types
    const totalCheckboxes = checkboxes.length;
    const checkedCheckboxes = Array.from(checkboxes).filter(cb => cb.checked).length;
    if (checkedCheckboxes < totalCheckboxes) count++;

    // Update filter count display
    if (count > 0) {
        filterCount.textContent = count;
        filterCount.classList.add('visible');
    } else {
        filterCount.classList.remove('visible');
    }
}

// Validate date range
function validateDateRange() {
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    if (startDate && endDate && startDate.value && endDate.value) {
        const start = new Date(startDate.value);
        const end = new Date(endDate.value);

        if (start > end) {
            console.log('Start date cannot be after end date - correcting automatically');
            endDate.value = startDate.value;
        }
    }
}

// Set default date range (last 30 days)
function setDefaultDateRange() {
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    if (startDate && endDate) {
        const today = new Date();
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);

        endDate.value = today.toISOString().split('T')[0];
        startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
}

function resetAllFilters() {
    const regionFilter = document.getElementById('regionFilter');
    const branchFilter = document.getElementById('branchFilter');
    const checkboxes = document.querySelectorAll('.filter-checkboxes input[type="checkbox"]');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const statusFilter = document.getElementById('statusFilter');

    // Reset to defaults
    if (regionFilter) {
        regionFilter.value = 'all';
        updateBranchOptions('all');
    }
    if (branchFilter) {
        branchFilter.value = 'all';
    }
    if (statusFilter) {
        statusFilter.value = 'all';
    }

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });

    // Reset date range to default (last 30 days)
    setDefaultDateRange();

    updateFilterCount();
    applyDashboardFilters();
}

function loadSavedFilters() {
    const savedFilters = localStorage.getItem('dashboard_filters');

    if (savedFilters) {
        try {
            const filters = JSON.parse(savedFilters);

            // Apply saved filters
            const regionFilter = document.getElementById('regionFilter');
            const branchFilter = document.getElementById('branchFilter');
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            const statusFilter = document.getElementById('statusFilter');

            if (filters.region && regionFilter) {
                regionFilter.value = filters.region;
                updateBranchOptions(filters.region);
            }

            if (filters.branch && branchFilter) {
                branchFilter.value = filters.branch;
            }

            if (filters.startDate && startDate) {
                startDate.value = filters.startDate;
            }

            if (filters.endDate && endDate) {
                endDate.value = filters.endDate;
            }

            if (filters.status && statusFilter) {
                statusFilter.value = filters.status;
            }

            if (filters.informationTypes) {
                Object.entries(filters.informationTypes).forEach(([key, value]) => {
                    const checkbox = document.getElementById(key);
                    if (checkbox) {
                        checkbox.checked = value;
                    }
                });
            }

            updateFilterCount();
            applyDashboardFilters();
        } catch (error) {
            console.error('Error loading saved filters:', error);
            // If loading fails, set default date range
            setDefaultDateRange();
        }
    } else {
        // No saved filters, set default date range
        setDefaultDateRange();
    }
}

function saveDashboardFilters() {
    const regionFilter = document.getElementById('regionFilter');
    const branchFilter = document.getElementById('branchFilter');
    const checkboxes = document.querySelectorAll('.filter-checkboxes input[type="checkbox"]');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const statusFilter = document.getElementById('statusFilter');

    const filters = {
        region: regionFilter ? regionFilter.value : 'all',
        branch: branchFilter ? branchFilter.value : 'all',
        startDate: startDate ? startDate.value : '',
        endDate: endDate ? endDate.value : '',
        status: statusFilter ? statusFilter.value : 'all',
        informationTypes: {}
    };

    checkboxes.forEach(checkbox => {
        filters.informationTypes[checkbox.id] = checkbox.checked;
    });

    localStorage.setItem('dashboard_filters', JSON.stringify(filters));
}

function applyDashboardFilters() {
    const regionFilter = document.getElementById('regionFilter');
    const branchFilter = document.getElementById('branchFilter');
    const primaryMetrics = document.getElementById('primaryMetrics');
    const tamsData = document.getElementById('tamsData');
    const sapInterface = document.getElementById('sapInterface');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const statusFilter = document.getElementById('statusFilter');

    // Get all dashboard cards
    const primaryCards = document.querySelectorAll('.primary-card');
    const secondaryCards = document.querySelectorAll('.secondary-card');
    const sapCards = document.querySelectorAll('.sap-card');

    // Apply information type filters
    primaryCards.forEach(card => {
        card.style.display = primaryMetrics && primaryMetrics.checked ? '' : 'none';
    });

    secondaryCards.forEach(card => {
        card.style.display = tamsData && tamsData.checked ? '' : 'none';
    });

    sapCards.forEach(card => {
        card.style.display = sapInterface && sapInterface.checked ? '' : 'none';
    });

    // Apply status filter to SAP cards
    if (statusFilter && statusFilter.value !== 'all') {
        sapCards.forEach(card => {
            const statusIndicator = card.querySelector('.status-indicator');
            if (statusIndicator) {
                const cardStatus = getCardStatus(statusIndicator);
                const shouldShow = matchesStatusFilter(cardStatus, statusFilter.value);
                if (sapInterface && sapInterface.checked) {
                    card.style.display = shouldShow ? '' : 'none';
                }
            }
        });
    }

    // Save filters
    saveDashboardFilters();

    console.log('Filters applied:', {
        region: regionFilter ? regionFilter.value : 'all',
        branch: branchFilter ? branchFilter.value : 'all',
        startDate: startDate ? startDate.value : '',
        endDate: endDate ? endDate.value : '',
        status: statusFilter ? statusFilter.value : 'all',
        primaryMetrics: primaryMetrics ? primaryMetrics.checked : true,
        tamsData: tamsData ? tamsData.checked : true,
        sapInterface: sapInterface ? sapInterface.checked : true
    });
}

// Helper function to get card status
function getCardStatus(statusIndicator) {
    if (statusIndicator.classList.contains('success')) return 'active';
    if (statusIndicator.classList.contains('warning')) return 'pending';
    if (statusIndicator.classList.contains('error')) return 'error';
    return 'active';
}

// Helper function to match status filter
function matchesStatusFilter(cardStatus, filterValue) {
    switch (filterValue) {
        case 'active': return cardStatus === 'active';
        case 'pending': return cardStatus === 'pending';
        case 'error': return cardStatus === 'error';
        case 'completed': return cardStatus === 'active'; // Treat active as completed
        default: return true;
    }
}

// Helper function to build filter summary
function buildFilterSummary() {
    const activeFilters = [];

    const regionFilter = document.getElementById('regionFilter');
    const branchFilter = document.getElementById('branchFilter');
    const statusFilter = document.getElementById('statusFilter');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    if (regionFilter && regionFilter.value !== 'all') {
        activeFilters.push(`Region: ${regionFilter.options[regionFilter.selectedIndex].text}`);
    }
    if (branchFilter && branchFilter.value !== 'all') {
        activeFilters.push(`Branch: ${branchFilter.options[branchFilter.selectedIndex].text}`);
    }
    if (statusFilter && statusFilter.value !== 'all') {
        activeFilters.push(`Status: ${statusFilter.options[statusFilter.selectedIndex].text}`);
    }
    if (startDate && startDate.value && endDate && endDate.value) {
        activeFilters.push(`Date Range: ${startDate.value} to ${endDate.value}`);
    }

    return activeFilters.length > 0 ? ` (${activeFilters.join(', ')})` : '';
}

// Update dashboard section based on navigation
function updateDashboardSection(section) {
    const pageTitle = document.querySelector('.page-title');
    const breadcrumb = document.querySelector('.breadcrumb');

    // Update page title and breadcrumb based on section
    switch(section) {
        case 'dashboard':
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
            break;
        case 'core':
            pageTitle.textContent = 'Core Dashboard';
            updateBreadcrumb(['Home', 'Core Dashboard']);
            break;
        case 'helpdesk':
            pageTitle.textContent = 'Helpdesk Dashboard';
            updateBreadcrumb(['Home', 'Helpdesk']);
            break;
        case 'parts':
            pageTitle.textContent = 'Parts Management';
            updateBreadcrumb(['Home', 'Parts']);
            break;
        case 'service':
            pageTitle.textContent = 'Service Management';
            updateBreadcrumb(['Home', 'Service']);
            break;
        case 'tams':
            pageTitle.textContent = 'TAMS Analytics';
            updateBreadcrumb(['Home', 'TAMS']);
            break;
        case 'scheduler':
            pageTitle.textContent = 'Scheduler';
            updateBreadcrumb(['Home', 'Scheduler']);
            break;
        default:
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
    }

    // In a real application, you would load different content here
    console.log(`Navigated to: ${section}`);
}

// Dropdown utility functions
function toggleDropdown(trigger, menu) {
    const isOpen = trigger.classList.contains('active');

    // Close all other dropdowns
    document.querySelectorAll('.icon-button.active, .admin-profile-button.active').forEach(btn => {
        if (btn !== trigger) {
            btn.classList.remove('active');
            btn.setAttribute('aria-expanded', 'false');
        }
    });

    document.querySelectorAll('.dropdown-menu.show').forEach(dropdown => {
        if (dropdown !== menu) {
            dropdown.classList.remove('show');
        }
    });

    // Toggle current dropdown
    if (isOpen) {
        closeDropdown(trigger, menu);
    } else {
        openDropdown(trigger, menu);
    }
}

function openDropdown(trigger, menu) {
    trigger.classList.add('active');
    trigger.setAttribute('aria-expanded', 'true');
    menu.classList.add('show');
}

function closeDropdown(trigger, menu) {
    trigger.classList.remove('active');
    trigger.setAttribute('aria-expanded', 'false');
    menu.classList.remove('show');
}

// Modal utility functions
function showModal(modal) {
    modal.classList.add('show');
    modal.setAttribute('aria-hidden', 'false');
    document.body.style.overflow = 'hidden';

    // Focus management
    const firstFocusable = modal.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
        firstFocusable.focus();
    }
}

function hideModal(modal) {
    modal.classList.remove('show');
    modal.setAttribute('aria-hidden', 'true');
    document.body.style.overflow = '';
}

// Advanced search functionality
function performAdvancedSearch(query, filter) {
    if (query.length < 2) {
        hideSuggestions();
        return;
    }

    console.log(`Searching for: "${query}" in category: ${filter}`);

    // Simulate search results based on filter
    const suggestions = generateSearchSuggestions(query, filter);
    displaySearchSuggestions(suggestions);
}

function generateSearchSuggestions(query, filter) {
    // Mock data for demonstration
    const mockData = {
        all: [
            { icon: 'directions_bus', text: 'Prevost H3-45 Coach', category: 'Vehicle' },
            { icon: 'person', text: 'Metro Transit Authority', category: 'Customer' },
            { icon: 'assessment', text: 'Monthly Sales Report', category: 'Report' },
            { icon: 'business', text: 'North Branch Inventory', category: 'Branch' }
        ],
        vehicles: [
            { icon: 'directions_bus', text: 'Prevost H3-45 Coach', category: 'Vehicle' },
            { icon: 'directions_bus', text: 'Prevost X3-45 Commuter', category: 'Vehicle' },
            { icon: 'directions_bus', text: 'Prevost H5-60 Volvo', category: 'Vehicle' }
        ],
        customers: [
            { icon: 'person', text: 'Metro Transit Authority', category: 'Customer' },
            { icon: 'person', text: 'City Bus Lines', category: 'Customer' },
            { icon: 'person', text: 'Regional Transport Co.', category: 'Customer' }
        ],
        reports: [
            { icon: 'assessment', text: 'Monthly Sales Report', category: 'Report' },
            { icon: 'assessment', text: 'Inventory Analysis', category: 'Report' },
            { icon: 'assessment', text: 'Customer Activity Report', category: 'Report' }
        ]
    };

    const data = mockData[filter] || mockData.all;

    // Filter based on query
    return data.filter(item =>
        item.text.toLowerCase().includes(query.toLowerCase()) ||
        item.category.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5); // Limit to 5 results
}

function displaySearchSuggestions(suggestions) {
    const searchSuggestions = document.getElementById('searchSuggestions');

    if (suggestions.length === 0) {
        searchSuggestions.innerHTML = `
            <div class="suggestion-item">
                <span class="material-icons suggestion-icon">search_off</span>
                <span class="suggestion-text">No results found</span>
            </div>
        `;
    } else {
        searchSuggestions.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item" data-type="${suggestion.category.toLowerCase()}">
                <span class="material-icons suggestion-icon">${suggestion.icon}</span>
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-category">${suggestion.category}</span>
            </div>
        `).join('');

        // Add click handlers to suggestions
        searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const text = this.querySelector('.suggestion-text').textContent;
                const type = this.dataset.type;

                // Update search input
                document.getElementById('globalSearch').value = text;

                // Hide suggestions
                hideSuggestions();

                // Handle selection
                handleSearchSelection(text, type);
            });
        });
    }

    searchSuggestions.classList.add('show');
}

function handleSearchSelection(text, type) {
    console.log(`Selected: ${text} (${type})`);

    // In a real application, this would:
    // 1. Navigate to the appropriate section
    // 2. Filter results based on the selection
    // 3. Update the main content area
}

function hideSuggestions() {
    const searchSuggestions = document.getElementById('searchSuggestions');
    searchSuggestions.classList.remove('show');
}

// Branch switching
function switchBranch(branchId) {
    console.log(`Switching to branch: ${branchId}`);

    // Store selected branch
    localStorage.setItem('selected_branch', branchId);

    // In a real application, this would:
    // 1. Update all data to show branch-specific information
    // 2. Refresh dashboard cards with branch data
    // 3. Update navigation context
}

// Release notes tabs
function initializeReleaseTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');
            document.getElementById(targetTab + 'Tab').classList.add('active');
        });
    });
}

// Customization options
function initializeCustomizationOptions() {
    // Layout options
    const layoutOptions = document.querySelectorAll('.layout-option');
    layoutOptions.forEach(option => {
        option.addEventListener('click', function() {
            layoutOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Theme options
    const themeOptions = document.querySelectorAll('.theme-option');
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            themeOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Load saved customizations
    loadCustomizationSettings();
}

function saveCustomizationSettings() {
    const settings = {
        layout: document.querySelector('.layout-option.active')?.dataset.layout || 'default',
        theme: document.querySelector('.theme-option.active')?.dataset.theme || 'default',
        widgets: {}
    };

    // Get widget visibility settings
    document.querySelectorAll('.widget-toggle input').forEach(input => {
        settings.widgets[input.dataset.widget] = input.checked;
    });

    localStorage.setItem('dashboard_customization', JSON.stringify(settings));

    // Apply settings
    applyCustomizationSettings(settings);
}

function loadCustomizationSettings() {
    const saved = localStorage.getItem('dashboard_customization');
    if (saved) {
        const settings = JSON.parse(saved);
        applyCustomizationSettings(settings);

        // Update UI to reflect saved settings
        document.querySelector(`[data-layout="${settings.layout}"]`)?.classList.add('active');
        document.querySelector(`[data-theme="${settings.theme}"]`)?.classList.add('active');

        Object.entries(settings.widgets || {}).forEach(([widget, visible]) => {
            const toggle = document.querySelector(`[data-widget="${widget}"]`);
            if (toggle) {
                toggle.checked = visible;
            }
        });
    }
}

function resetCustomizationSettings() {
    localStorage.removeItem('dashboard_customization');

    // Reset UI to defaults
    document.querySelectorAll('.layout-option').forEach(opt => opt.classList.remove('active'));
    document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
    document.querySelector('[data-layout="default"]')?.classList.add('active');
    document.querySelector('[data-theme="default"]')?.classList.add('active');

    document.querySelectorAll('.widget-toggle input').forEach(input => {
        input.checked = true;
    });

    // Apply default settings
    applyCustomizationSettings({
        layout: 'default',
        theme: 'default',
        widgets: {}
    });
}

function applyCustomizationSettings(settings) {
    // Apply layout
    document.body.className = document.body.className.replace(/layout-\w+/g, '');
    document.body.classList.add(`layout-${settings.layout}`);

    // Apply theme
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${settings.theme}`);

    // Apply widget visibility
    Object.entries(settings.widgets || {}).forEach(([widget, visible]) => {
        const element = document.querySelector(`[data-widget-id="${widget}"]`);
        if (element) {
            element.style.display = visible ? '' : 'none';
        }
    });
}

// Month-end process functionality
function initializeMonthEndActions() {
    const actionButtons = document.querySelectorAll('.action-btn');

    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            handleMonthEndAction(action);
        });
    });
}

function handleMonthEndAction(action) {
    switch(action) {
        case 'generate-reports':
            console.log('Generating month-end reports...');
            // Simulate report generation
            setTimeout(() => {
                console.log('Reports generated successfully');
            }, 2000);
            break;

        case 'reconcile-inventory':
            console.log('Starting inventory reconciliation...');
            // Simulate reconciliation process
            setTimeout(() => {
                console.log('Inventory reconciliation completed');
            }, 3000);
            break;

        case 'close-period':
            if (confirm('Are you sure you want to close the current period? This action cannot be undone.')) {
                console.log('Closing period...');
                setTimeout(() => {
                    console.log('Period closed successfully');
                }, 2000);
            }
            break;

        case 'export-data':
            console.log('Preparing data export...');
            // Simulate data export
            setTimeout(() => {
                console.log('Data export ready for download');
                // In a real application, this would trigger a file download
            }, 1500);
            break;
    }
}

// Admin profile actions
function handleAdminAction(action) {
    switch(action) {
        case 'profile':
            console.log('Opening profile settings...');
            // In a real application, this would open a profile settings page/modal
            break;

        case 'preferences':
            console.log('Opening preferences...');
            // In a real application, this would open a preferences page/modal
            break;

        case 'security':
            console.log('Opening security settings...');
            // In a real application, this would open security settings
            break;

        case 'notifications':
            console.log('Opening notification settings...');
            // In a real application, this would open notification preferences
            break;

        case 'help':
            console.log('Opening help documentation...');
            // In a real application, this would open help/documentation
            break;

        case 'about':
            console.log('Opening about information...');
            // In a real application, this would show version/about info
            break;
    }
}

function handleLogout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear stored user data
        localStorage.removeItem('dashboard_user_name');
        localStorage.removeItem('dashboard_user_role');
        localStorage.removeItem('prevost_remembered_credentials');
        localStorage.removeItem('selected_branch');
        localStorage.removeItem('dashboard_customization');

        // Redirect to login page
        window.location.href = 'index.html';
    }
}

// Notification system removed - functionality preserved without popup notifications

// Initialize logout functionality
function initializeLogout() {
    // Logout is now handled through the admin profile dropdown
    // But we keep this function for backward compatibility
}

// Initialize dashboard interactions
function initializeDashboardInteractions() {
    // Add hover effects and click handlers for dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');

    dashboardCards.forEach(card => {
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('.card-title').textContent;
            showCardDetails(cardTitle);
        });
    });

    // Add click handlers for activity items
    const activityItems = document.querySelectorAll('.activity-item');

    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            const activityText = this.querySelector('.activity-text').textContent;
            showActivityDetails(activityText);
        });
    });

    // Initialize enhanced card interactions
    initializeCardInteractions();
}

// Update page content based on navigation
function updatePageContent(section) {
    const pageTitle = document.querySelector('.page-title');
    const breadcrumb = document.querySelector('.breadcrumb');

    // Update page title and breadcrumb based on section
    switch(section) {
        case 'overview':
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
            break;
        case 'inventory':
            pageTitle.textContent = 'Vehicle Inventory';
            updateBreadcrumb(['Home', 'Inventory']);
            break;
        case 'sales':
            pageTitle.textContent = 'Sales Management';
            updateBreadcrumb(['Home', 'Sales']);
            break;
        case 'customers':
            pageTitle.textContent = 'Customer Management';
            updateBreadcrumb(['Home', 'Customers']);
            break;
        case 'branches':
            pageTitle.textContent = 'Branch Management';
            updateBreadcrumb(['Home', 'Management', 'Branches']);
            break;
        case 'users':
            pageTitle.textContent = 'User Management';
            updateBreadcrumb(['Home', 'Management', 'Users']);
            break;
        case 'reports':
            pageTitle.textContent = 'Reports & Analytics';
            updateBreadcrumb(['Home', 'Management', 'Reports']);
            break;
        case 'settings':
            pageTitle.textContent = 'System Settings';
            updateBreadcrumb(['Home', 'Management', 'Settings']);
            break;
        default:
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
    }

    // In a real application, you would load different content here
    console.log(`Navigated to: ${section}`);
}

// Update breadcrumb navigation
function updateBreadcrumb(items) {
    const breadcrumb = document.querySelector('.breadcrumb');
    breadcrumb.innerHTML = '';

    items.forEach((item, index) => {
        const span = document.createElement('span');
        span.textContent = item;
        breadcrumb.appendChild(span);

        if (index < items.length - 1) {
            const icon = document.createElement('span');
            icon.className = 'material-icons';
            icon.textContent = 'chevron_right';
            breadcrumb.appendChild(icon);
        }
    });
}

// Show card details (placeholder functionality)
function showCardDetails(cardTitle) {
    let detailMessage = '';

    // Customize message based on card type
    if (cardTitle.includes('SAP Interface')) {
        detailMessage = `SAP Interface Details: ${cardTitle}\n\nThis would show:\n- Synchronization status\n- Last sync time\n- Error logs (if any)\n- Manual sync options`;
    } else if (cardTitle.includes('TAMS')) {
        detailMessage = `TAMS Metrics: ${cardTitle}\n\nThis would show:\n- Detailed performance metrics\n- Historical trends\n- Benchmark comparisons\n- Action recommendations`;
    } else if (cardTitle.includes('Summary')) {
        detailMessage = `Business Metrics: ${cardTitle}\n\nThis would show:\n- Detailed breakdowns\n- Trend analysis\n- Comparative data\n- Drill-down options`;
    } else {
        detailMessage = `Viewing details for: ${cardTitle}\n\nIn a real application, this would open a detailed view or modal with more information about ${cardTitle.toLowerCase()}.`;
    }

    alert(detailMessage);
}

// Show activity details (placeholder functionality)
function showActivityDetails(activityText) {
    alert(`Activity Details:\n\n${activityText}\n\nIn a real application, this would show more detailed information about this activity.`);
}

// Load dashboard data (simulated)
function loadDashboardData() {
    // Simulate loading dashboard data
    console.log('Loading dashboard data...');

    // In a real application, you would make API calls here to load:
    // - Quotation data and conversion ratios
    // - Work order statistics
    // - Warranty claim information
    // - Invoice summaries
    // - TAMS metrics
    // - SAP interface status
    // - Recent activities

    // Simulate updating SAP interface statuses
    setTimeout(() => {
        updateSAPInterfaceStatuses();
    }, 2000);

    // For now, we'll just log that data is loaded
    setTimeout(() => {
        console.log('Dashboard data loaded successfully');

        // Add a subtle animation to indicate data is loaded
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px) scale(0.95)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0) scale(1)';
                }, 100);
            }, index * 80); // Staggered animation for visual appeal
        });

        // Animate progress circles and charts
        setTimeout(() => {
            animateProgressCircles();
            animateChartBars();
        }, 1500);
    }, 1000);
}

// Update SAP interface statuses (simulated)
function updateSAPInterfaceStatuses() {
    const sapCards = document.querySelectorAll('.sap-card');

    sapCards.forEach((card, index) => {
        const statusIndicator = card.querySelector('.status-indicator');
        const statusText = card.querySelector('.status-text');

        // Simulate random status updates
        const statuses = [
            { class: 'success', text: 'Synchronized' },
            { class: 'warning', text: 'Pending' },
            { class: 'error', text: 'Error' }
        ];

        // Most interfaces should be successful
        const randomStatus = Math.random() < 0.8 ? statuses[0] :
                           Math.random() < 0.7 ? statuses[1] : statuses[2];

        // Update status with animation
        setTimeout(() => {
            statusIndicator.className = `status-indicator ${randomStatus.class}`;
            statusText.textContent = randomStatus.text;

            // Add a subtle pulse animation
            statusIndicator.style.animation = 'pulse 0.5s ease';
            setTimeout(() => {
                statusIndicator.style.animation = '';
            }, 500);
        }, index * 200);
    });
}

// Enhanced progress circle animations
function animateProgressCircles() {
    const progressBars = document.querySelectorAll('.progress-bar');

    progressBars.forEach((bar, index) => {
        const dashArray = bar.getAttribute('stroke-dasharray');
        if (dashArray) {
            const [progress] = dashArray.split(',');

            // Start from 0 and animate to target with enhanced easing
            bar.setAttribute('stroke-dasharray', '0, 100');

            setTimeout(() => {
                bar.style.transition = 'stroke-dasharray 2s cubic-bezier(0.4, 0, 0.2, 1)';
                bar.setAttribute('stroke-dasharray', dashArray);

                // Add a subtle glow effect during animation
                bar.style.filter = 'drop-shadow(0 0 8px currentColor)';
                setTimeout(() => {
                    bar.style.filter = 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))';
                }, 2000);
            }, index * 150);
        }
    });
}

// Enhanced chart bar animations
function animateChartBars() {
    const chartBars = document.querySelectorAll('.chart-bar');

    chartBars.forEach((bar, index) => {
        const targetHeight = bar.style.height;

        // Start from 0 height with enhanced animation
        bar.style.height = '0%';
        bar.style.transform = 'scaleY(0)';
        bar.style.transition = 'height 1.2s cubic-bezier(0.4, 0, 0.2, 1), transform 1.2s cubic-bezier(0.4, 0, 0.2, 1)';

        setTimeout(() => {
            bar.style.height = targetHeight;
            bar.style.transform = 'scaleY(1)';

            // Add a subtle bounce effect
            setTimeout(() => {
                bar.style.transform = 'scaleY(1.05)';
                setTimeout(() => {
                    bar.style.transform = 'scaleY(1)';
                }, 150);
            }, 1200);
        }, index * 80);
    });
}

// Enhanced card interactions with modern effects
function initializeCardInteractions() {
    const cards = document.querySelectorAll('.dashboard-card');

    cards.forEach((card, cardIndex) => {
        // Add focus support for accessibility
        card.setAttribute('tabindex', '0');
        card.setAttribute('role', 'button');

        // Enhanced mouse interactions
        card.addEventListener('mouseenter', function() {
            // Enhance hover effect with subtle chart animations
            const chartBars = this.querySelectorAll('.chart-bar');
            chartBars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.transform = 'scaleY(1.15)';
                    bar.style.filter = 'brightness(1.1)';
                }, index * 50);
            });

            const progressCircle = this.querySelector('.progress-circle');
            if (progressCircle) {
                progressCircle.style.transform = 'scale(1.1) rotate(5deg)';
            }

            // Add subtle glow to status indicators
            const statusIndicators = this.querySelectorAll('.status-indicator');
            statusIndicators.forEach(indicator => {
                indicator.style.boxShadow = '0 0 0 6px rgba(var(--primary-500), 0.3)';
            });
        });

        card.addEventListener('mouseleave', function() {
            // Reset animations with smooth transitions
            const chartBars = this.querySelectorAll('.chart-bar');
            chartBars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.transform = 'scaleY(1)';
                    bar.style.filter = 'brightness(1)';
                }, index * 30);
            });

            const progressCircle = this.querySelector('.progress-circle');
            if (progressCircle) {
                progressCircle.style.transform = 'scale(1) rotate(0deg)';
            }

            // Reset status indicator glow
            const statusIndicators = this.querySelectorAll('.status-indicator');
            statusIndicators.forEach(indicator => {
                indicator.style.boxShadow = '';
            });
        });

        // Add click ripple effect
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.className = 'ripple-effect';

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });

        // Keyboard support
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
}

// Add ripple animation keyframes
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

// Utility function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Handle window resize for responsive behavior
window.addEventListener('resize', function() {
    // Adjust layout if needed for responsive design
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        // Mobile-specific adjustments
        document.body.classList.add('mobile-view');
    } else {
        // Desktop-specific adjustments
        document.body.classList.remove('mobile-view');
    }
});

// Initialize responsive behavior on load
window.addEventListener('load', function() {
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        document.body.classList.add('mobile-view');
    }
});

// Global event listeners
document.addEventListener('click', function(event) {
    // Close dropdowns when clicking outside
    if (!event.target.closest('.header-dropdown')) {
        document.querySelectorAll('.icon-button.active, .admin-profile-button.active').forEach(trigger => {
            const menu = trigger.parentElement.querySelector('.dropdown-menu');
            if (menu) {
                closeDropdown(trigger, menu);
            }
        });
    }

    // Close modals when clicking on overlay
    if (event.target.classList.contains('modal-overlay')) {
        hideModal(event.target);
    }
});

// Keyboard navigation support
document.addEventListener('keydown', function(event) {
    // ESC key to close modals and dropdowns
    if (event.key === 'Escape') {
        // Close any open modals
        document.querySelectorAll('.modal-overlay.show').forEach(modal => {
            hideModal(modal);
        });

        // Close any open dropdowns
        document.querySelectorAll('.icon-button.active, .admin-profile-button.active').forEach(trigger => {
            const menu = trigger.parentElement.querySelector('.dropdown-menu');
            if (menu) {
                closeDropdown(trigger, menu);
            }
        });

        // Close filter panel
        closeFilterPanel();
    }

    // Ctrl/Cmd + L for logout
    if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
        event.preventDefault();
        handleLogout();
    }

    // Arrow key navigation for dropdowns
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        const activeDropdown = document.querySelector('.dropdown-menu.show');
        if (activeDropdown) {
            event.preventDefault();
            navigateDropdown(activeDropdown, event.key === 'ArrowDown');
        }
    }

    // Enter key to select dropdown item
    if (event.key === 'Enter') {
        const focusedItem = document.querySelector('.dropdown-item:focus');
        if (focusedItem) {
            focusedItem.click();
        }
    }
});

function navigateDropdown(dropdown, down) {
    const items = dropdown.querySelectorAll('.dropdown-item');
    const currentFocus = dropdown.querySelector('.dropdown-item:focus');
    let index = currentFocus ? Array.from(items).indexOf(currentFocus) : -1;

    if (down) {
        index = index < items.length - 1 ? index + 1 : 0;
    } else {
        index = index > 0 ? index - 1 : items.length - 1;
    }

    items[index].focus();
}

// Add accessibility enhancements
function enhanceAccessibility() {
    // Add ARIA labels and roles for horizontal navigation
    const horizontalNavLinks = document.querySelectorAll('.horizontal-nav .nav-link');
    horizontalNavLinks.forEach(link => {
        link.setAttribute('role', 'tab');
        link.setAttribute('tabindex', '0');
        link.setAttribute('aria-selected', link.parentElement.classList.contains('active'));
    });

    // Add keyboard support for horizontal nav links
    horizontalNavLinks.forEach(link => {
        link.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.click();
            }
        });
    });

    // Make dropdown items focusable
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        item.setAttribute('tabindex', '0');
        item.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.click();
            }
        });
    });

    // Add ARIA labels to buttons
    const headerButtons = document.querySelectorAll('.header-button');
    headerButtons.forEach(button => {
        if (!button.getAttribute('aria-label')) {
            const text = button.querySelector('.button-text')?.textContent ||
                        button.textContent.trim();
            button.setAttribute('aria-label', text);
        }
    });

    // Add live region for notifications
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.id = 'live-region';
    document.body.appendChild(liveRegion);
}

// Initialize accessibility enhancements
enhanceAccessibility();

/* ========================================
   COMPONENT INTEGRATION
   ======================================== */

// Initialize component integration when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize component integration after main dashboard initialization
    setTimeout(() => {
        initializeComponentIntegration();
    }, 100);
});

// Initialize component integration
function initializeComponentIntegration() {
    console.log('Initializing component integration...');

    // Initialize enhanced search functionality
    initializeEnhancedSearch();
    console.log('Enhanced search initialized');

    // Initialize sidebar functionality
    initializeSidebarComponents();
    console.log('Sidebar components initialized');

    // Initialize menu bar functionality
    initializeMenuBarComponents();
    console.log('Menu bar components initialized');

    // Initialize bookmark management
    initializeBookmarkManagement();
    console.log('Bookmark management initialized');

    // Initialize component keyboard shortcuts
    initializeComponentKeyboardShortcuts();
    console.log('Component keyboard shortcuts initialized');

    console.log('Component integration complete!');
}

// Initialize enhanced search functionality
function initializeEnhancedSearch() {
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');
    const searchOverlay = document.getElementById('search-overlay');
    const searchClear = document.getElementById('searchClear');
    const filterButtons = document.querySelectorAll('.filter-btn');

    if (!searchInput || !searchResults) return;

    let searchTimeout;
    let selectedIndex = -1;

    // Handle search input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (query.length > 0) {
                performEnhancedSearch(query);
                showSearchResults();
            } else {
                hideSearchResults();
            }
        }, 300);
    });

    // Handle search focus
    searchInput.addEventListener('focus', function() {
        if (this.value.trim().length > 0) {
            showSearchResults();
        }
    });

    // Handle search clear
    if (searchClear) {
        searchClear.addEventListener('click', function() {
            searchInput.value = '';
            hideSearchResults();
            searchInput.focus();
        });
    }

    // Handle filter buttons
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active filter
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Re-search with new filter if there's a query
            const query = searchInput.value.trim();
            if (query.length > 0) {
                performEnhancedSearch(query);
                showSearchResults();
            }
        });
    });

    // Handle keyboard navigation
    searchInput.addEventListener('keydown', function(e) {
        const items = searchResults.querySelectorAll('.search-item');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
            updateSearchSelection(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            updateSearchSelection(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selectedIndex >= 0 && items[selectedIndex]) {
                items[selectedIndex].click();
            }
        } else if (e.key === 'Escape') {
            hideSearchResults();
        }
    });

    // Handle overlay click
    if (searchOverlay) {
        searchOverlay.addEventListener('click', hideSearchResults);
    }

    // Handle results close button
    const resultsClose = document.getElementById('search-results-close');
    if (resultsClose) {
        resultsClose.addEventListener('click', hideSearchResults);
    }

    function showSearchResults() {
        searchResults.classList.add('active');
        searchOverlay.classList.add('active');
        selectedIndex = -1;
    }

    function hideSearchResults() {
        searchResults.classList.remove('active');
        searchOverlay.classList.remove('active');
        selectedIndex = -1;
    }

    function updateSearchSelection(items) {
        items.forEach((item, index) => {
            if (index === selectedIndex) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }
}

// Perform enhanced search
function performEnhancedSearch(query) {
    const searchData = window.searchData || [];
    const results = searchData.filter(item =>
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase()) ||
        item.category.toLowerCase().includes(query.toLowerCase())
    );

    displaySearchResults(results, query);
}

// Display search results
function displaySearchResults(results, query) {
    const searchItems = document.getElementById('search-items');
    const resultsCount = document.querySelector('.results-count');
    const searchTerm = document.querySelector('.search-term');
    const noResults = document.getElementById('search-no-results');

    if (!searchItems) return;

    // Update results info
    if (resultsCount) {
        resultsCount.textContent = `${results.length} result${results.length !== 1 ? 's' : ''}`;
    }
    if (searchTerm) {
        searchTerm.textContent = `for "${query}"`;
    }

    // Clear previous results
    searchItems.innerHTML = '';

    if (results.length === 0) {
        if (noResults) {
            noResults.style.display = 'block';
        }
        return;
    }

    if (noResults) {
        noResults.style.display = 'none';
    }

    // Display results
    results.forEach(item => {
        const resultElement = createSearchResultElement(item, query);
        searchItems.appendChild(resultElement);
    });
}

// Create search result element
function createSearchResultElement(item, query) {
    const element = document.createElement('button');
    element.className = 'search-item';
    element.innerHTML = `
        <div class="search-item-icon">
            <span class="material-icons">${item.icon || 'description'}</span>
        </div>
        <div class="search-item-content">
            <div class="search-item-title">${highlightText(item.name, query)}</div>
            <div class="search-item-description">${highlightText(item.description, query)}</div>
        </div>
        <div class="search-item-meta">
            <div class="search-item-category">${item.category}</div>
        </div>
    `;

    element.addEventListener('click', () => {
        if (item.href) {
            if (item.href.startsWith('#')) {
                // Internal navigation
                const section = item.href.substring(1);
                navigateToSection(section);
            } else {
                // External navigation
                window.location.href = item.href;
            }
        }
        hideSearchResults();
    });

    return element;
}

// Highlight search text
function highlightText(text, query) {
    if (!query) return text;
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// Initialize sidebar components
function initializeSidebarComponents() {
    console.log('Initializing sidebar components...');

    const leftToggle = document.getElementById('sidebar-toggle-left');
    const rightToggle = document.getElementById('sidebar-toggle-right');
    const leftSidebar = document.getElementById('sidebar-left');
    const rightSidebar = document.getElementById('sidebar-right');
    const overlay = document.getElementById('sidebar-overlay');

    console.log('Sidebar elements found:', {
        leftToggle: !!leftToggle,
        rightToggle: !!rightToggle,
        leftSidebar: !!leftSidebar,
        rightSidebar: !!rightSidebar,
        overlay: !!overlay
    });

    // Initialize sidebar toggles
    if (leftToggle && leftSidebar) {
        leftToggle.addEventListener('click', () => {
            console.log('Left sidebar toggle clicked');
            toggleSidebar(leftSidebar, overlay);
        });
        console.log('Left sidebar toggle initialized');
    }

    if (rightToggle && rightSidebar) {
        rightToggle.addEventListener('click', () => {
            console.log('Right sidebar toggle clicked');
            toggleSidebar(rightSidebar, overlay);
        });
        console.log('Right sidebar toggle initialized');
    }

    // Initialize sidebar close buttons
    const leftClose = document.getElementById('sidebar-left-close');
    const rightClose = document.getElementById('sidebar-right-close');

    if (leftClose) {
        leftClose.addEventListener('click', () => closeSidebar(leftSidebar, overlay));
    }

    if (rightClose) {
        rightClose.addEventListener('click', () => closeSidebar(rightSidebar, overlay));
    }

    // Handle overlay click
    if (overlay) {
        overlay.addEventListener('click', () => {
            closeSidebar(leftSidebar, overlay);
            closeSidebar(rightSidebar, overlay);
        });
    }

    // Load bookmark data
    loadBookmarkData();
}

// Toggle sidebar
function toggleSidebar(sidebar, overlay) {
    console.log('Toggling sidebar:', sidebar?.id);
    if (sidebar && sidebar.classList.contains('open')) {
        console.log('Closing sidebar');
        closeSidebar(sidebar, overlay);
    } else {
        console.log('Opening sidebar');
        openSidebar(sidebar, overlay);
    }
}

// Open sidebar
function openSidebar(sidebar, overlay) {
    console.log('Opening sidebar:', sidebar?.id);
    if (sidebar) {
        sidebar.classList.add('open');
        console.log('Added open class to sidebar');
    }
    if (overlay) {
        overlay.classList.add('active');
        console.log('Added active class to overlay');
    }
}

// Close sidebar
function closeSidebar(sidebar, overlay) {
    console.log('Closing sidebar:', sidebar?.id);
    if (sidebar) {
        sidebar.classList.remove('open');
        console.log('Removed open class from sidebar');
    }
    if (overlay) {
        overlay.classList.remove('active');
        console.log('Removed active class from overlay');
    }
}

// Load bookmark data
function loadBookmarkData() {
    const bookmarks = window.sampleBookmarks || [];
    const categories = window.sampleCategories || [];

    // Load left sidebar bookmarks (favorites)
    const leftBookmarkList = document.getElementById('bookmark-list');
    if (leftBookmarkList) {
        const favoriteBookmarks = bookmarks.filter(b => b.category === 'favorites');
        populateBookmarkList(leftBookmarkList, favoriteBookmarks);
    }

    // Load right sidebar bookmarks (recent)
    const rightBookmarkList = document.getElementById('bookmark-list-right');
    if (rightBookmarkList) {
        const recentBookmarks = bookmarks.filter(b => b.category === 'recent');
        populateBookmarkList(rightBookmarkList, recentBookmarks);
    }

    // Load quick access
    const quickAccessList = document.getElementById('quick-access-list');
    if (quickAccessList) {
        const quickAccessItems = bookmarks.slice(0, 4); // Top 4 most used
        populateBookmarkList(quickAccessList, quickAccessItems);
    }

    // Load categories
    const categoryList = document.getElementById('category-list');
    if (categoryList) {
        populateCategoryList(categoryList, categories);
    }
}

// Populate bookmark list
function populateBookmarkList(container, bookmarks) {
    container.innerHTML = '';

    bookmarks.forEach(bookmark => {
        const element = document.createElement('button');
        element.className = 'bookmark-item';
        element.innerHTML = `
            <div class="bookmark-item-icon">
                <span class="material-icons">${bookmark.icon}</span>
            </div>
            <div class="bookmark-item-content">
                <div class="bookmark-item-title">${bookmark.name}</div>
                <div class="bookmark-item-description">${bookmark.description}</div>
            </div>
            <div class="bookmark-item-meta">
                <div class="bookmark-usage">${bookmark.usage || 0}</div>
                <button class="bookmark-remove" title="Remove bookmark">
                    <span class="material-icons">close</span>
                </button>
            </div>
        `;

        element.addEventListener('click', (e) => {
            if (!e.target.closest('.bookmark-remove')) {
                navigateToBookmark(bookmark);
            }
        });

        // Handle remove button
        const removeBtn = element.querySelector('.bookmark-remove');
        removeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            removeBookmark(bookmark.id);
        });

        container.appendChild(element);
    });
}

// Populate category list
function populateCategoryList(container, categories) {
    container.innerHTML = '';

    categories.forEach(category => {
        const element = document.createElement('button');
        element.className = 'category-item';
        element.innerHTML = `
            <div class="category-item-icon">
                <span class="material-icons">${category.icon}</span>
            </div>
            <div class="category-item-content">
                <div class="category-item-title">${category.name}</div>
            </div>
        `;

        element.addEventListener('click', () => {
            filterBookmarksByCategory(category.id);
        });

        container.appendChild(element);
    });
}

// Navigate to bookmark
function navigateToBookmark(bookmark) {
    if (bookmark.href) {
        if (bookmark.href.startsWith('#')) {
            const section = bookmark.href.substring(1);
            navigateToSection(section);
        } else {
            window.location.href = bookmark.href;
        }
    }

    // Close sidebars
    const leftSidebar = document.getElementById('sidebar-left');
    const rightSidebar = document.getElementById('sidebar-right');
    const overlay = document.getElementById('sidebar-overlay');

    closeSidebar(leftSidebar, overlay);
    closeSidebar(rightSidebar, overlay);
}

// Initialize menu bar components
function initializeMenuBarComponents() {
    // Initialize dropdown menu buttons (only those with data-dropdown attribute)
    const menuButtons = document.querySelectorAll('.menu-button[data-dropdown]');

    menuButtons.forEach(button => {
        const dropdownId = button.dataset.dropdown;
        const dropdown = document.getElementById(dropdownId + '-menu');

        if (dropdown) {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                toggleMegaMenu(button, dropdown);
            });
        }
    });

    // Initialize regular navigation links (those without data-dropdown)
    const regularNavLinks = document.querySelectorAll('.menu-bar .nav-link:not(.menu-button)');

    regularNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Close any open mega menus
            closeAllMegaMenus();

            // Get the target section
            const section = this.dataset.section;

            // Navigate to the section
            if (section) {
                navigateToSection(section);
            }
        });
    });

    // Initialize icon buttons
    const iconButtons = document.querySelectorAll('.menu-icon-buttons .icon-button');

    iconButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            const action = button.dataset.action;
            handleIconButtonAction(action);
        });
    });

    // Initialize mega menu tabs
    initializeMegaMenuTabs();

    // Initialize mega menu search
    initializeMegaMenuSearch();

    // Close mega menus when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.menu-item')) {
            closeAllMegaMenus();
        }
    });
}

// Toggle mega menu
function toggleMegaMenu(button, dropdown) {
    const isActive = dropdown.classList.contains('active');

    // Close all other mega menus
    closeAllMegaMenus();

    if (!isActive) {
        dropdown.classList.add('active');
        button.parentElement.classList.add('active');
    }
}

// Close all mega menus
function closeAllMegaMenus() {
    const activeMenus = document.querySelectorAll('.dropdown-menu.active');
    const activeButtons = document.querySelectorAll('.menu-item.active');

    activeMenus.forEach(menu => menu.classList.remove('active'));
    activeButtons.forEach(button => button.classList.remove('active'));
}

// Initialize mega menu tabs
function initializeMegaMenuTabs() {
    const tabButtons = document.querySelectorAll('.mega-menu-tab');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.dataset.tab;
            const megaMenu = button.closest('.dropdown-menu');

            if (megaMenu) {
                // Update tab buttons
                megaMenu.querySelectorAll('.mega-menu-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                button.classList.add('active');

                // Update tab content
                megaMenu.querySelectorAll('.mega-menu-tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                const targetContent = megaMenu.querySelector(`#${tabId}-content`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            }
        });
    });
}

// Initialize mega menu search
function initializeMegaMenuSearch() {
    const searchInputs = document.querySelectorAll('.mega-menu-search-input');

    searchInputs.forEach(input => {
        let searchTimeout;

        input.addEventListener('input', function() {
            const query = this.value.trim();
            const megaMenu = this.closest('.dropdown-menu');

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterMegaMenuItems(megaMenu, query);
            }, 300);
        });
    });
}

// Filter mega menu items
function filterMegaMenuItems(megaMenu, query) {
    if (!megaMenu) return;

    const cards = megaMenu.querySelectorAll('.mega-card');

    cards.forEach(card => {
        const title = card.querySelector('h4').textContent.toLowerCase();
        const description = card.querySelector('p').textContent.toLowerCase();
        const searchQuery = query.toLowerCase();

        if (title.includes(searchQuery) || description.includes(searchQuery) || !query) {
            card.style.display = 'flex';
        } else {
            card.style.display = 'none';
        }
    });
}

// Handle icon button actions
function handleIconButtonAction(action) {
    switch (action) {
        case 'bookmarks':
            const leftSidebar = document.getElementById('sidebar-left');
            const overlay = document.getElementById('sidebar-overlay');
            toggleSidebar(leftSidebar, overlay);
            break;
        case 'dashboard-settings':
            console.log('Dashboard Settings clicked');
            // Add your dashboard settings logic here
            break;
        case 'load-unload-dashboard':
            console.log('Load/Unload Dashboard clicked');
            // Add your load/unload logic here
            break;
        case 'kpi-reports':
            console.log('KPI Reports clicked');
            // Add your KPI reports logic here
            break;
        case 'month-end-process':
            console.log('Month End Process clicked');
            // Add your month end process logic here
            break;
        case 'user-manual':
            console.log('User Manual clicked');
            // Add your user manual logic here
            break;
        default:
            console.log(`Unknown icon action: ${action}`);
    }
}

// Initialize bookmark management
function initializeBookmarkManagement() {
    // Initialize bookmark modal
    const manageButtons = document.querySelectorAll('.bookmark-manage-btn');
    const bookmarkModal = document.getElementById('bookmark-modal');
    const bookmarkModalClose = document.getElementById('bookmark-modal-close');

    manageButtons.forEach(button => {
        button.addEventListener('click', () => {
            showBookmarkModal();
        });
    });

    if (bookmarkModalClose) {
        bookmarkModalClose.addEventListener('click', hideBookmarkModal);
    }

    // Initialize add bookmark modal
    const addButtons = document.querySelectorAll('[id^="add-bookmark-btn"]');
    const addBookmarkModal = document.getElementById('add-bookmark-modal');
    const addBookmarkClose = document.getElementById('add-bookmark-modal-close');

    addButtons.forEach(button => {
        button.addEventListener('click', () => {
            showAddBookmarkModal();
        });
    });

    if (addBookmarkClose) {
        addBookmarkClose.addEventListener('click', hideAddBookmarkModal);
    }

    // Initialize bookmark form
    const addBookmarkSave = document.getElementById('add-bookmark-save');
    if (addBookmarkSave) {
        addBookmarkSave.addEventListener('click', saveNewBookmark);
    }
}

// Show bookmark modal
function showBookmarkModal() {
    const modal = document.getElementById('bookmark-modal');
    if (modal) {
        modal.classList.add('active');
    }
}

// Hide bookmark modal
function hideBookmarkModal() {
    const modal = document.getElementById('bookmark-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// Show add bookmark modal
function showAddBookmarkModal() {
    const modal = document.getElementById('add-bookmark-modal');
    if (modal) {
        modal.classList.add('active');
    }
}

// Hide add bookmark modal
function hideAddBookmarkModal() {
    const modal = document.getElementById('add-bookmark-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// Initialize component keyboard shortcuts
function initializeComponentKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl+K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl+B for bookmarks
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            const leftSidebar = document.getElementById('sidebar-left');
            const overlay = document.getElementById('sidebar-overlay');
            toggleSidebar(leftSidebar, overlay);
        }

        // Escape to close modals and dropdowns
        if (e.key === 'Escape') {
            hideBookmarkModal();
            hideAddBookmarkModal();
            closeAllMegaMenus();

            const searchResults = document.getElementById('search-results');
            if (searchResults && searchResults.classList.contains('active')) {
                hideSearchResults();
            }

            // Close sidebars
            const leftSidebar = document.getElementById('sidebar-left');
            const rightSidebar = document.getElementById('sidebar-right');
            const overlay = document.getElementById('sidebar-overlay');
            closeSidebar(leftSidebar, overlay);
            closeSidebar(rightSidebar, overlay);
        }
    });
}

// Add missing functions for search results
function hideSearchResults() {
    const searchResults = document.getElementById('search-results');
    const searchOverlay = document.getElementById('search-overlay');

    if (searchResults) {
        searchResults.classList.remove('active');
    }
    if (searchOverlay) {
        searchOverlay.classList.remove('active');
    }
}

// Add missing functions for bookmark management
function removeBookmark(bookmarkId) {
    console.log(`Removing bookmark: ${bookmarkId}`);
    // Add your bookmark removal logic here
    loadBookmarkData(); // Refresh the bookmark lists
}

function filterBookmarksByCategory(categoryId) {
    console.log(`Filtering bookmarks by category: ${categoryId}`);
    // Add your category filtering logic here
}

function saveNewBookmark() {
    const form = document.getElementById('add-bookmark-form');
    if (form) {
        const formData = new FormData(form);
        const bookmark = {
            id: Date.now().toString(),
            name: formData.get('name'),
            href: formData.get('url'),
            icon: formData.get('icon') || 'bookmark',
            category: formData.get('category'),
            description: formData.get('description'),
            usage: 0
        };

        console.log('Saving new bookmark:', bookmark);
        // Add your bookmark saving logic here

        // Add to sample data for demo
        window.sampleBookmarks.push(bookmark);
        loadBookmarkData();

        // Close modal and reset form
        hideAddBookmarkModal();
        form.reset();
    }
}
