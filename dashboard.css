/* CSS Custom Properties (Variables) */
:root {
    /* Color Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    --secondary-50: #f0fdf4;
    --secondary-100: #dcfce7;
    --secondary-200: #bbf7d0;
    --secondary-300: #86efac;
    --secondary-400: #4ade80;
    --secondary-500: #22c55e;
    --secondary-600: #16a34a;
    --secondary-700: #15803d;
    --secondary-800: #166534;
    --secondary-900: #14532d;

    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;

    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;

    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-200: #fecaca;
    --error-300: #fca5a5;
    --error-400: #f87171;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-800: #991b1b;
    --error-900: #7f1d1d;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
    --gradient-success: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Typography */
    --font-sans: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-serif: 'Cambria', Georgia, serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cambria', serif;
    background-color: #ffffff;
    color: #2c2c2c;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

#root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.dashboard-header {
    background-color: #000000;
    color: #ffffff;
    padding: 1rem 2rem;
    border-bottom: 1px solid #333333;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-logo {
    height: 40px;
    width: auto;
}

.header-title h1 {
    font-family: 'Cambria', serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-subtitle {
    font-size: 0.875rem;
    color: #cccccc;
    font-weight: 400;
}

/* Header Center - Advanced Search */
.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
}

.advanced-search-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.search-wrapper {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-wrapper:focus-within {
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.advanced-search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 3rem;
    background: transparent;
    border: none;
    color: #ffffff;
    font-family: 'Cambria', serif;
    font-size: 0.95rem;
    font-weight: 400;
}

.advanced-search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
}

.advanced-search-input:focus {
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 20px;
    pointer-events: none;
    z-index: 2;
}

.search-filters {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 0.25rem;
    z-index: 2;
}

.filter-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 16px;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    transform: scale(1.1);
}

.filter-btn.active {
    background: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.search-clear {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    display: none;
    transition: all 0.3s ease;
    z-index: 2;
}

.search-clear:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.search-clear.visible {
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-suggestions {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.search-suggestions.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.suggestion-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: background-color 0.3s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    color: #666666;
    font-size: 18px;
}

.suggestion-text {
    flex: 1;
    color: #2c2c2c;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.suggestion-category {
    font-size: 0.8rem;
    color: #999999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Icon Buttons */
.icon-button {
    position: relative;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.icon-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon-button .material-icons {
    font-size: 20px;
}

.month-end-button {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.4);
}

.month-end-button:hover {
    background-color: rgba(76, 175, 80, 0.3);
    border-color: rgba(76, 175, 80, 0.5);
}

.logout-button {
    background-color: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.3);
}

.logout-button:hover {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.4);
}

.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #f44336;
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Admin Profile Button */
.admin-profile-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    min-width: 120px;
}

.admin-profile-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
}

.admin-name {
    font-weight: 600;
    font-size: 0.85rem;
    line-height: 1.2;
}

.admin-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.2;
}

/* Tooltips */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    top: calc(100% + 8px);
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-family: 'Cambria', serif;
    white-space: nowrap;
    z-index: 2000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

[data-tooltip]:hover::after {
    content: '';
    position: absolute;
    top: calc(100% + 2px);
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Header Dropdowns */
.header-dropdown {
    position: relative;
}

.dropdown-arrow {
    transition: transform 0.3s ease;
    font-size: 16px;
}

.admin-profile-button.active .dropdown-arrow,
.icon-button.active .dropdown-arrow {
    transform: rotate(180deg);
}

.icon-button.active,
.admin-profile-button.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #2c2c2c;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.dropdown-item .material-icons {
    font-size: 18px;
}

.dropdown-separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 0.5rem 0;
}

.logout-item {
    color: #d32f2f;
}

.logout-item:hover {
    background-color: #ffebee;
}

/* User Profile in Admin Dropdown */
.user-profile {
    padding: 0.5rem 1rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: #cccccc;
}

.user-avatar {
    margin-left: 0.5rem;
}

.user-avatar .material-icons {
    font-size: 24px;
}

/* Branch Selector Specific */
.branch-selector .dropdown-menu {
    left: 0;
    right: auto;
}

/* Horizontal Navigation */
.horizontal-nav {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 80px;
    z-index: 50;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    color: #666666;
    text-decoration: none;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    position: relative;
}

.nav-link:hover {
    color: #2c2c2c;
    background-color: rgba(44, 44, 44, 0.05);
}

.nav-item.active .nav-link {
    color: #2c2c2c;
    border-bottom-color: #2c2c2c;
    background-color: rgba(44, 44, 44, 0.08);
}

.nav-link .material-icons {
    font-size: 20px;
}

.nav-text {
    font-weight: 600;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    min-height: calc(100vh - 140px);
    position: relative;
}

/* Content Area */
.dashboard-content {
    padding: 2rem;
    background-color: #ffffff;
    width: 100%;
    overflow-x: hidden;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    background-color: #ffffff;
    z-index: 10;
    gap: 2rem;
}

.header-left {
    flex: 1;
}

.page-title {
    font-family: 'Cambria', serif;
    font-size: 2rem;
    font-weight: 700;
    color: #2c2c2c;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666666;
    font-size: 0.875rem;
}

.breadcrumb .material-icons {
    font-size: 16px;
}

/* Filter Panel */
/* Modern Filter Panel - Enhanced positioning */

/* Enhanced Filter Toggle Button */
.filter-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid transparent;
    color: var(--neutral-700);
    padding: var(--space-4) var(--space-5);
    border-radius: var(--radius-xl);
    cursor: pointer;
    font-family: var(--font-sans);
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

/* Gradient border effect */
.filter-toggle::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.filter-toggle:hover::before {
    opacity: 1;
}

.filter-toggle:hover {
    background: rgba(255, 255, 255, 1);
    color: var(--primary-600);
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.filter-toggle:focus-visible {
    outline: 3px solid var(--primary-500);
    outline-offset: 2px;
}

.filter-toggle.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-xl), 0 0 30px rgba(14, 165, 233, 0.3);
    transform: translateY(-2px);
}

.filter-toggle.active::before {
    opacity: 0;
}

.filter-toggle .material-icons {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.filter-toggle.active .material-icons {
    transform: rotate(180deg);
}

/* Enhanced Filter Count Badge */
.filter-count {
    background: var(--gradient-error);
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-xl);
    min-width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
    line-height: 1;
    font-family: var(--font-mono);
    box-shadow: var(--shadow-md);
    animation: pulse 2s infinite;
    position: relative;
}

.filter-count::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: var(--gradient-error);
    border-radius: inherit;
    opacity: 0.3;
    animation: ripple 2s infinite;
}

.filter-count.visible {
    display: flex;
}

.filter-toggle.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.filter-toggle.active .filter-count::before {
    background: rgba(255, 255, 255, 0.2);
}

/* Modern Filter Dropdown Panel */
.filter-dropdown {
    position: absolute;
    top: calc(100% + 12px);
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl), 0 0 40px rgba(0, 0, 0, 0.1);
    min-width: 380px;
    max-width: 420px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* Ensure filter dropdown stays within viewport bounds */
.filter-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* Dynamic positioning for filter dropdown to prevent overflow */
@media (max-width: 1200px) {
    .filter-dropdown {
        right: 0;
        left: auto;
        min-width: 360px;
        max-width: 380px;
    }
}

@media (max-width: 900px) {
    .filter-dropdown {
        right: 0;
        left: auto;
        min-width: 320px;
        max-width: 340px;
    }
}

/* Prevent filter dropdown from extending beyond left edge */
.filter-panel {
    position: relative;
    flex-shrink: 0;
}

.filter-panel .filter-dropdown {
    /* Use transform to ensure dropdown stays within bounds */
    transform-origin: top right;
}

/* Alternative positioning when dropdown would overflow left */
.filter-dropdown.position-left {
    right: auto;
    left: 0;
    transform-origin: top left;
}

.filter-dropdown::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-glass);
    opacity: 0.5;
    pointer-events: none;
}

/* Modern Filter Header */
.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) var(--space-6) var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
}

.filter-header h3 {
    font-family: var(--font-sans);
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--neutral-800);
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Reset Button */
.filter-reset {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--neutral-600);
    font-size: 0.8rem;
    cursor: pointer;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: var(--font-sans);
    font-weight: 600;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.filter-reset:hover {
    background: var(--error-50);
    color: var(--error-600);
    border-color: var(--error-200);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.filter-reset .material-icons {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.filter-reset:hover .material-icons {
    transform: rotate(180deg);
}

/* Modern Filter Sections */
.filter-section {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.filter-section:last-of-type {
    border-bottom: none;
    padding-bottom: var(--space-6);
}

.filter-label {
    display: block;
    font-family: var(--font-sans);
    font-size: 0.85rem;
    font-weight: 700;
    color: var(--neutral-700);
    margin-bottom: var(--space-3);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Modern Filter Select */
.filter-select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--neutral-700);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-400);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    transform: translateY(-1px);
}

.filter-select:hover {
    border-color: var(--primary-300);
    background: rgba(255, 255, 255, 0.9);
}

/* Modern Filter Checkboxes */
.filter-checkboxes {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    cursor: pointer;
    font-family: var(--font-sans);
    font-size: 0.9rem;
    color: var(--neutral-700);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.checkbox-item:hover {
    background: rgba(255, 255, 255, 0.6);
    transform: translateX(4px);
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

/* Enhanced Custom Checkbox */
.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.8);
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    transform: scale(1.1);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.2);
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 6px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
    0% {
        opacity: 0;
        transform: rotate(45deg) scale(0);
    }
    100% {
        opacity: 1;
        transform: rotate(45deg) scale(1);
    }
}

.checkbox-label {
    flex: 1;
    user-select: none;
    font-weight: 500;
}

/* Modern Filter Actions */
.filter-actions {
    padding: var(--space-5) var(--space-6) var(--space-6);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
}

.btn-apply {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-xl);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.btn-apply::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn-apply:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl), 0 0 30px rgba(14, 165, 233, 0.3);
}

.btn-apply:hover::before {
    opacity: 1;
}

.btn-apply:active {
    transform: translateY(0);
}

/* Modern Dashboard Cards Grid */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-12);
    max-width: 100%;
}

/* Modern Card Design with Glassmorphism */
.dashboard-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    display: flex;
    flex-direction: column;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    min-height: 320px;
    cursor: pointer;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.5);
}

.dashboard-card:hover::before {
    opacity: 1;
}

/* Card Category Accent Bars */
.dashboard-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* Modern Card Header */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-6);
    position: relative;
    z-index: 2;
}

.card-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-icon::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: inherit;
    padding: 2px;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.3));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dashboard-card:hover .card-icon::before {
    opacity: 1;
}

.card-icon .material-icons {
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Primary Business Metrics Cards */
.primary-card::after {
    background: var(--gradient-primary);
}

.primary-card .card-icon {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg), 0 0 20px rgba(14, 165, 233, 0.3);
}

.primary-card:hover .card-icon {
    box-shadow: var(--shadow-xl), 0 0 30px rgba(14, 165, 233, 0.4);
    transform: scale(1.1) rotate(5deg);
}

/* Secondary Cards (TAMS) */
.secondary-card::after {
    background: var(--gradient-success);
}

.secondary-card .card-icon {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow-lg), 0 0 20px rgba(34, 197, 94, 0.3);
}

.secondary-card:hover .card-icon {
    box-shadow: var(--shadow-xl), 0 0 30px rgba(34, 197, 94, 0.4);
    transform: scale(1.1) rotate(-5deg);
}

/* SAP Interface Cards */
.sap-card::after {
    background: var(--gradient-warning);
}

.sap-card .card-icon {
    background: var(--gradient-warning);
    color: white;
    box-shadow: var(--shadow-lg), 0 0 20px rgba(245, 158, 11, 0.3);
}

.sap-card:hover .card-icon {
    box-shadow: var(--shadow-xl), 0 0 30px rgba(245, 158, 11, 0.4);
    transform: scale(1.1) rotate(5deg);
}

/* Modern Card Content */
.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.card-title {
    font-family: var(--font-sans);
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin-bottom: var(--space-2);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.2;
}

.card-subtitle {
    font-family: var(--font-sans);
    font-size: 0.75rem;
    color: var(--neutral-400);
    margin-bottom: var(--space-3);
    font-weight: 500;
}

.card-main-content {
    margin: var(--space-6) 0;
    text-align: center;
    position: relative;
}

.card-value {
    font-family: var(--font-sans);
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--neutral-800);
    margin-bottom: var(--space-3);
    line-height: 0.9;
    letter-spacing: -0.025em;
    position: relative;
}

.card-value::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    border-radius: 1px;
    opacity: 0.3;
}

.primary-card .card-value::after {
    background: var(--primary-500);
}

.secondary-card .card-value::after {
    background: var(--success-500);
}

.sap-card .card-value::after {
    background: var(--warning-500);
}

.secondary-card .card-value {
    font-size: 2.25rem;
}

.sap-card .card-value {
    font-size: 1.5rem;
}

/* Modern Card Metrics */
.card-metric {
    font-size: 0.8rem;
    color: var(--neutral-600);
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
    padding: var(--space-2) var(--space-3);
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    font-family: var(--font-sans);
}

.metric-value {
    font-weight: 700;
    font-size: 0.85rem;
    font-family: var(--font-mono);
}

.primary-card .metric-value {
    color: var(--primary-600);
}

.secondary-card .metric-value {
    color: var(--success-600);
}

.sap-card .metric-value {
    color: var(--warning-600);
}

/* Modern Data Visualization Elements */
.card-chart {
    height: 80px;
    margin: var(--space-4) 0;
    position: relative;
}

/* Enhanced Progress Circles */
.progress-circle {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-circle::before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1), transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dashboard-card:hover .progress-circle::before {
    opacity: 1;
}

.dashboard-card:hover .progress-circle {
    transform: scale(1.1);
}

.progress-circle svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.progress-circle .progress-bg {
    fill: none;
    stroke: var(--neutral-200);
    stroke-width: 6;
}

.progress-circle .progress-bar {
    fill: none;
    stroke-width: 6;
    stroke-linecap: round;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.primary-card .progress-bar {
    stroke: url(#primaryGradient);
}

.secondary-card .progress-bar {
    stroke: url(#successGradient);
}

.sap-card .progress-bar {
    stroke: url(#warningGradient);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--neutral-700);
    font-family: var(--font-sans);
}

/* Modern Mini Charts */
.mini-chart {
    height: 50px;
    margin: var(--space-4) 0;
    display: flex;
    align-items: end;
    justify-content: space-between;
    gap: 3px;
    padding: var(--space-2);
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.chart-bar {
    flex: 1;
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
    min-height: 10px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: bottom;
    position: relative;
    overflow: hidden;
}

.chart-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), transparent);
    border-radius: inherit;
}

.dashboard-card:hover .chart-bar {
    transform: scaleY(1.1);
}

.primary-card .chart-bar {
    background: var(--gradient-primary);
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
}

.secondary-card .chart-bar {
    background: var(--gradient-success);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.sap-card .chart-bar {
    background: var(--gradient-warning);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* Enhanced Trend Indicators */
.trend-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: 0.75rem;
    font-weight: 700;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
    font-family: var(--font-sans);
}

.trend-up {
    color: var(--success-600);
    background: rgba(34, 197, 94, 0.1);
}

.trend-down {
    color: var(--error-600);
    background: rgba(239, 68, 68, 0.1);
}

.trend-neutral {
    color: var(--neutral-600);
    background: rgba(115, 115, 115, 0.1);
}

.trend-indicator .material-icons {
    font-size: 16px;
}

/* Modern SAP Status Indicators */
.sap-status {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-top: var(--space-4);
    padding: var(--space-2) var(--space-3);
    background: rgba(255, 255, 255, 0.6);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    animation: pulse 2s infinite;
}

.status-indicator::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: 50%;
    opacity: 0.3;
    animation: ripple 2s infinite;
}

.status-indicator.success {
    background: var(--success-500);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

.status-indicator.success::before {
    background: var(--success-500);
}

.status-indicator.warning {
    background: var(--warning-500);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
}

.status-indicator.warning::before {
    background: var(--warning-500);
}

.status-indicator.error {
    background: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.status-indicator.error::before {
    background: var(--error-500);
}

.status-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--neutral-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-family: var(--font-sans);
}

/* Modern Animations and Micro-interactions */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes ripple {
    0% { transform: scale(0.8); opacity: 1; }
    100% { transform: scale(1.4); opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Card Loading Animation */
.dashboard-card.loading {
    overflow: hidden;
    position: relative;
}

.dashboard-card.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Staggered Animation for Cards */
.dashboard-card:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.dashboard-card:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.dashboard-card:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.dashboard-card:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.dashboard-card:nth-child(5) { animation: fadeInUp 0.6s ease-out 0.5s both; }
.dashboard-card:nth-child(6) { animation: fadeInUp 0.6s ease-out 0.6s both; }
.dashboard-card:nth-child(7) { animation: fadeInUp 0.6s ease-out 0.7s both; }
.dashboard-card:nth-child(8) { animation: fadeInUp 0.6s ease-out 0.8s both; }

/* Smooth Transitions */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* Legacy styles for compatibility */
.card-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.card-change.positive {
    color: #2e7d32;
}

.card-change.negative {
    color: #d32f2f;
}

.card-change.neutral {
    color: #666666;
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 2rem;
}

.section-title {
    font-family: 'Cambria', serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

/* Activity List */
.activity-list {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-icon {
    background-color: #f0f0f0;
    color: #2c2c2c;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-icon .material-icons {
    font-size: 20px;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #666666;
}

/* Footer */
.dashboard-footer {
    background-color: #000000;
    color: #ffffff;
    padding: 1.5rem 2rem;
    border-top: 1px solid #333333;
    margin-top: auto;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
}

.footer-content p {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.footer-link {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #ffffff;
}

.footer-separator {
    color: #666666;
    font-size: 0.875rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.modal-header h2 {
    font-family: 'Cambria', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c2c2c;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: #e0e0e0;
    color: #2c2c2c;
}

.modal-content {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

/* Release Notes Modal */
.release-notes-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e0e0e0;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #666666;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #2c2c2c;
    border-bottom-color: #2c2c2c;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.release-note {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.release-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.release-header h3 {
    font-family: 'Cambria', serif;
    font-size: 1.2rem;
    color: #2c2c2c;
    margin: 0;
}

.release-date {
    font-size: 0.875rem;
    color: #666666;
}

.release-body h4 {
    font-family: 'Cambria', serif;
    font-size: 1rem;
    color: #2c2c2c;
    margin: 1rem 0 0.5rem 0;
}

.release-body ul {
    margin: 0 0 1rem 1.5rem;
    color: #555555;
}

.release-body li {
    margin-bottom: 0.25rem;
}

/* Customization Modal */
.customize-section {
    margin-bottom: 2rem;
}

.customize-section h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

.layout-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.layout-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.layout-option:hover {
    border-color: #cccccc;
}

.layout-option.active {
    border-color: #2c2c2c;
    background-color: #f8f9fa;
}

.layout-preview {
    margin-bottom: 0.5rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    height: 60px;
    background-color: #f0f0f0;
    border-radius: 4px;
    padding: 4px;
}

.preview-grid.compact {
    grid-template-columns: 1fr 1fr 1fr;
}

.preview-grid.wide {
    grid-template-columns: 1fr;
}

.preview-card {
    background-color: #2c2c2c;
    border-radius: 2px;
}

.preview-card.small {
    height: 15px;
}

.preview-card.wide {
    height: 25px;
}

.widget-toggles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.widget-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: #cccccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.widget-toggle input[type="checkbox"] {
    display: none;
}

.widget-toggle input[type="checkbox"]:checked + .toggle-slider {
    background-color: #2c2c2c;
}

.widget-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
}

.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.theme-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.theme-option:hover {
    border-color: #cccccc;
}

.theme-option.active {
    border-color: #2c2c2c;
    background-color: #f8f9fa;
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    margin: 0 auto 0.5rem;
}

.theme-preview.default {
    background: linear-gradient(135deg, #ffffff 50%, #f8f9fa 50%);
    border: 1px solid #e0e0e0;
}

.theme-preview.dark {
    background: linear-gradient(135deg, #2c2c2c 50%, #000000 50%);
}

.theme-preview.blue {
    background: linear-gradient(135deg, #1976d2 50%, #0d47a1 50%);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background-color: #2c2c2c;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1a1a1a;
}

.btn-secondary {
    background-color: transparent;
    color: #666666;
    border: 1px solid #cccccc;
}

.btn-secondary:hover {
    background-color: #f8f9fa;
    border-color: #999999;
}

/* Month-End Modal */
.month-end-status h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1.5rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.status-item.completed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-item.in-progress {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-item.pending {
    background-color: #f5f5f5;
    color: #666666;
}

.month-end-actions h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #2c2c2c;
}

.action-btn:hover {
    background-color: #e9ecef;
    border-color: #cccccc;
    transform: translateY(-2px);
}

.action-btn .material-icons {
    font-size: 24px;
    color: #666666;
}

/* Notification System - Removed */

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.dropdown-item:focus,
.header-button:focus,
.dropdown-trigger:focus,
.search-input:focus,
.nav-link:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

.modal-close:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        gap: 1rem;
    }

    .header-center {
        max-width: 350px;
    }

    .search-filters {
        display: none;
    }

    .advanced-search-input {
        padding-right: 3rem;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .filter-dropdown {
        min-width: 280px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 0.75rem 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .header-left {
        justify-content: center;
    }

    .header-center {
        order: 3;
        max-width: none;
    }

    .header-right {
        order: 2;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    /* Horizontal Navigation */
    .horizontal-nav {
        top: 120px;
    }

    .nav-container {
        padding: 0 0.5rem;
    }

    .nav-menu {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .nav-menu::-webkit-scrollbar {
        display: none;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        white-space: nowrap;
        min-width: fit-content;
    }

    .nav-text {
        display: none;
    }

    /* Content adjustments */
    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .filter-panel {
        align-self: flex-end;
    }

    .filter-dropdown {
        right: 0;
        left: auto;
        min-width: calc(100vw - 2rem);
        max-width: 320px;
    }

    .dashboard-main {
        min-height: calc(100vh - 180px);
    }

    .icon-button {
        width: 36px;
        height: 36px;
    }

    .icon-button .material-icons {
        font-size: 18px;
    }

    .admin-profile-button {
        padding: 0.4rem 0.6rem;
        min-width: 100px;
    }

    .admin-name {
        font-size: 0.8rem;
    }

    .admin-role {
        font-size: 0.7rem;
    }

    .search-wrapper {
        border-radius: 20px;
    }

    .advanced-search-input {
        padding: 0.6rem 2.5rem 0.6rem 2.5rem;
        font-size: 0.9rem;
    }

    .search-icon {
        left: 0.75rem;
        font-size: 18px;
    }

    .search-clear {
        right: 0.6rem;
    }

    .filter-btn {
        width: 24px;
        height: 24px;
        font-size: 14px;
    }

    [data-tooltip]:hover::before,
    [data-tooltip]:hover::after {
        display: none;
    }

    .modal-container {
        max-width: 95vw;
        margin: 1rem;
    }

    .modal-content {
        padding: 1rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .dashboard-card {
        min-height: 250px;
    }

    .layout-options,
    .theme-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 0.25rem;
    }

    .icon-button {
        width: 32px;
        height: 32px;
    }

    .icon-button .material-icons {
        font-size: 16px;
    }

    .admin-profile-button {
        min-width: 80px;
        padding: 0.3rem 0.5rem;
    }

    .admin-name {
        font-size: 0.75rem;
    }

    .admin-role {
        font-size: 0.65rem;
    }

    .notification-badge {
        top: -4px;
        right: -4px;
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    /* Navigation adjustments */
    .nav-link {
        padding: 0.5rem 0.75rem;
    }

    .nav-link .material-icons {
        font-size: 18px;
    }

    /* Dashboard adjustments */
    .dashboard-content {
        padding: 1rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-card {
        min-height: 180px;
        padding: 1rem;
    }

    .card-value {
        font-size: 1.2rem;
    }

    .filter-dropdown {
        min-width: calc(100vw - 2rem);
        max-width: calc(100vw - 2rem);
        left: 1rem;
        right: 1rem;
        top: calc(100% + 8px);
    }

    .filter-toggle {
        min-width: 100px;
        padding: var(--space-3) var(--space-4);
    }

    .filter-toggle .filter-text {
        display: none;
    }

    .filter-section {
        padding: var(--space-4) var(--space-5);
    }

    .filter-header {
        padding: var(--space-5) var(--space-5) var(--space-3);
    }

    .date-range-inputs {
        flex-direction: column;
        gap: var(--space-2);
    }

    .filter-date {
        width: 100%;
    }

    .date-separator {
        transform: rotate(90deg);
        font-size: 0.75rem;
    }
}

    .dashboard-main {
        flex-direction: column;
    }

    .dashboard-nav {
        width: 100%;
        min-height: auto;
        order: 2;
    }

    .dashboard-content {
        order: 1;
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-4);
    }

    .dashboard-card {
        padding: var(--space-4);
        min-height: 280px;
    }

    .card-value {
        font-size: 2rem;
    }

    .secondary-card .card-value {
        font-size: 1.8rem;
    }

    .sap-card .card-value {
        font-size: 1.3rem;
    }

    .progress-circle {
        width: 60px;
        height: 60px;
    }

    .progress-text {
        font-size: 0.8rem;
    }

    .mini-chart {
        height: 40px;
    }

    .card-icon {
        width: 48px;
        height: 48px;
    }

    .card-icon .material-icons {
        font-size: 24px;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.25rem;
    }

    .footer-separator {
        display: none;
    }
}

@media (max-width: 600px) {
    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .dashboard-card {
        min-height: 240px;
        padding: var(--space-4);
    }

    .card-value {
        font-size: 1.8rem;
    }

    .secondary-card .card-value {
        font-size: 1.6rem;
    }

    .sap-card .card-value {
        font-size: 1.2rem;
    }

    .progress-circle {
        width: 50px;
        height: 50px;
    }

    .mini-chart {
        height: 35px;
    }

    .card-icon {
        width: 40px;
        height: 40px;
    }

    .card-icon .material-icons {
        font-size: 20px;
    }

    /* Disable hover effects on mobile */
    .dashboard-card:hover {
        transform: none;
        box-shadow: var(--shadow-lg);
    }

    .dashboard-card:hover .card-icon {
        transform: none;
    }
}

@media (max-width: 480px) {
    .header-logo {
        height: 30px;
    }

    .header-title h1 {
        font-size: 1.2rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .card-value {
        font-size: 1.5rem;
    }

    .nav-section {
        padding: 0 1rem;
    }
}

/* Modern Date Range Components */
.date-range-inputs {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2);
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.filter-date {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    font-family: var(--font-sans);
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.8);
    color: var(--neutral-700);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    font-weight: 500;
}

.filter-date:focus {
    outline: none;
    border-color: var(--primary-400);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    transform: translateY(-1px);
}

.filter-date:hover {
    border-color: var(--primary-300);
    background: rgba(255, 255, 255, 0.9);
}

.date-separator {
    color: var(--neutral-500);
    font-size: 0.85rem;
    font-weight: 600;
    font-family: var(--font-sans);
    padding: 0 var(--space-1);
}

/* Month-End Modal Styles */
.month-end-section {
    padding: 1rem 0;
}

.section-description {
    color: #666666;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    border: 2px solid transparent;
    border-radius: 8px;
    background-color: #f8f9fa;
    color: #2c2c2c;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-family: 'Cambria', serif;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
    background-color: #e3f2fd;
    border-color: #1976d2;
}

.action-btn.primary:hover {
    background-color: #bbdefb;
}

.action-btn.secondary {
    background-color: #f3e5f5;
    border-color: #7b1fa2;
}

.action-btn.secondary:hover {
    background-color: #e1bee7;
}

.action-btn.warning {
    background-color: #fff3e0;
    border-color: #f57c00;
}

.action-btn.warning:hover {
    background-color: #ffe0b2;
}

.action-btn.info {
    background-color: #e0f2f1;
    border-color: #00796b;
}

.action-btn.info:hover {
    background-color: #b2dfdb;
}

.action-btn .material-icons {
    font-size: 32px;
    margin-bottom: 0.5rem;
}

.action-text {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.action-description {
    font-size: 0.85rem;
    color: #666666;
    line-height: 1.4;
}

.month-end-status {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.month-end-status h4 {
    margin-bottom: 1rem;
    color: #2c2c2c;
    font-size: 1.1rem;
}

.status-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.status-item.completed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-item.completed .material-icons {
    color: #4caf50;
}

.status-item.pending {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-item.pending .material-icons {
    color: #ff9800;
}

/* Enhanced Customization Modal Styles */
.customize-section {
    margin-bottom: 2rem;
}

.customize-section h3 {
    margin-bottom: 1rem;
    color: #2c2c2c;
    font-size: 1.1rem;
}

.layout-options,
.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.layout-option,
.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.layout-option:hover,
.theme-option:hover {
    border-color: #1976d2;
    background-color: #f8f9fa;
}

.layout-option.active,
.theme-option.active {
    border-color: #1976d2;
    background-color: #e3f2fd;
}

.layout-preview,
.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    height: 100%;
    padding: 2px;
}

.preview-grid.compact {
    grid-template-columns: 1fr 1fr 1fr;
}

.preview-grid.wide {
    grid-template-columns: 1fr;
}

.preview-card {
    background-color: #ddd;
    border-radius: 2px;
}

.preview-card.small {
    background-color: #ccc;
}

.preview-card.wide {
    background-color: #bbb;
}

.theme-preview.default {
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
}

.theme-preview.dark {
    background: linear-gradient(135deg, #333333, #1a1a1a);
}

.theme-preview.blue {
    background: linear-gradient(135deg, #e3f2fd, #1976d2);
}

.widget-toggles {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.widget-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background-color: #ccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.widget-toggle input:checked + .toggle-slider {
    background-color: #1976d2;
}

.widget-toggle input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.widget-toggle input {
    display: none;
}

.toggle-label {
    font-size: 0.9rem;
    color: #2c2c2c;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-primary,
.btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #1976d2;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1565c0;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #2c2c2c;
    border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
    background-color: #eeeeee;
    transform: translateY(-1px);
}

/* Modern Utility Classes */
.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-glow {
    box-shadow: var(--shadow-xl), 0 0 40px rgba(14, 165, 233, 0.2);
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Focus States */
.dashboard-card:focus-visible {
    outline: 3px solid var(--primary-500);
    outline-offset: 2px;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Filter Panel Loading State */
.filter-dropdown.loading {
    pointer-events: none;
}

.filter-dropdown.loading::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Filter Panel Focus Management */
.filter-dropdown:focus-within {
    box-shadow: var(--shadow-2xl), 0 0 0 3px rgba(14, 165, 233, 0.2);
}

/* Enhanced Filter Animations */
.filter-section {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-section:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Filter Success State */
.filter-applied {
    animation: filterSuccess 0.6s ease-out;
}

@keyframes filterSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .dashboard-card:hover {
        transform: none;
    }

    .progress-circle,
    .card-icon,
    .chart-bar {
        transition: none;
    }

    .filter-dropdown {
        transition: opacity 0.2s ease;
    }

    .filter-toggle {
        transition: background-color 0.2s ease;
    }
}